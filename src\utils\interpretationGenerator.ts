// Interpretación cualitativa basada en los puntajes del MACI-II

interface ScoreData {
  scaleId: string;
  scaleName: string;
  pd: number;
  pc: number | string;
}

interface PatientData {
  first_name: string;
  last_name: string;
  birth_date: string;
  gender: string;
}

// Definiciones de interpretación por escala
const PERSONALITY_PATTERNS_INTERPRETATIONS = {
  '1': {
    name: 'Introvertido',
    interpretations: {
      low: 'Sociable, expresivo, busca interacciones.',
      moderate: 'Tiende a ser reservado y prefiere actividades solitarias, aunque puede interactuar socialmente cuando es necesario. Puede parecer algo distante o indiferente emocionalmente.',
      significant: 'Marcada preferencia por la soledad y el desapego social. Dificultad para experimentar o expresar emociones profundas. Puede tener pocas amistades y dificultades en la comprensión de las sutilezas sociales.',
      prominent: 'Patrón generalizado de desinterés social y emocional. El adolescente es un observador pasivo de la vida, con una capacidad limitada para la calidez o el afecto, y una marcada indiferencia hacia las relaciones.'
    }
  },
  '2': {
    name: 'Inhibido',
    interpretations: {
      low: 'Socialmente seguro, confiado, no teme el rechazo.',
      moderate: 'Cauteloso en situaciones sociales, sensible a la crítica o al rechazo. Puede evitar situaciones nuevas o donde se sienta evaluado.',
      significant: 'Aprensión social significativa. Experimenta malestar y evitación activa de situaciones sociales por temor a la humillación o el rechazo. Baja autoestima y autocrítica.',
      prominent: 'Patrón generalizado de inhibición social, sentimientos de inadecuación e hipersensibilidad a la evaluación negativa. Miedo y desconfianza guían su postura social, con tendencia a la ansiedad y tristeza.'
    }
  },
  '3': {
    name: 'Sumiso',
    interpretations: {
      low: 'Independiente, asertivo, no busca excesivamente la aprobación.',
      moderate: 'Busca agradar a los demás y puede tener dificultades para tomar decisiones por sí mismo. Tiende a depender de otros para obtener apoyo.',
      significant: 'Fuerte necesidad de dependencia y pasividad en las relaciones. Evita el conflicto y subordina sus deseos para mantener el afecto. Falta de autoconfianza e indecisión.',
      prominent: 'Patrón generalizado de comportamiento sumiso y dependiente. Busca activamente relaciones en las que pueda asumir un rol subordinado para asegurar cuidado y afecto. Marcada falta de iniciativa y autonomía.'
    }
  },
  '4': {
    name: 'Dramático',
    interpretations: {
      low: 'Comportamiento y expresión emocional mesurados y estables.',
      moderate: 'Busca atención y puede ser percibido como encantador pero algo superficial. Sus emociones pueden ser intensas pero cambiantes.',
      significant: 'Búsqueda constante de atención y afecto a través de comportamientos encantadores e incluso seductores. Las relaciones tienden a ser superficiales y las emociones lábiles.',
      prominent: 'Patrón generalizado de búsqueda de atención y emocionalidad excesiva. El adolescente necesita ser el centro de atención, utilizando su encanto y a veces la dramatización para lograrlo, con relaciones superficiales y dificultad para desarrollar una identidad cohesiva.'
    }
  },
  '5': {
    name: 'Egocéntrico',
    interpretations: {
      low: 'Humilde, considerado con los demás, empático.',
      moderate: 'Confianza en sí mismo, puede tener altas expectativas sobre sus capacidades y buscar admiración, aunque aún muestra cierta consideración por los demás.',
      significant: 'Autopercepción de superioridad y especial merecimiento. Fuerte deseo de admiración y tendencia a la auto-focalización, lo que dificulta la empatía y puede llevar a la explotación pasiva de otros.',
      prominent: 'Patrón generalizado de grandiosidad, necesidad de admiración y falta de empatía. Se considera especial, espera un trato preferencial y explota a los demás para sus propios fines sin reciprocidad.'
    }
  },
  '6A': {
    name: 'Rebelde',
    interpretations: {
      low: 'Respetuoso de las normas, cooperador, considerado.',
      moderate: 'Cuestiona la autoridad y puede mostrarse algo desafiante o impulsivo. Puede tener dificultades para seguir las reglas consistentemente.',
      significant: 'Percepción del mundo como hostil y desconfianza hacia los demás. Valora la autonomía y actúa para satisfacer sus necesidades sin considerar las reglas sociales o el impacto en otros. Tendencia a la impulsividad.',
      prominent: 'Patrón generalizado de desconfianza, desafío a la autoridad y comportamiento antisocial. Vive el momento, busca gratificación inmediata y muestra poca preocupación por las consecuencias o los sentimientos ajenos, presentando a menudo problemas de conducta.'
    }
  },
  '6B': {
    name: 'Hostil',
    interpretations: {
      low: 'Amable, empático, busca relaciones cooperativas.',
      moderate: 'Puede ser asertivo y algo dominante, con tendencia a imponer su voluntad. Podría mostrar irritabilidad o baja tolerancia a la frustración.',
      significant: 'Fuerte, asertivo y con tendencia a la hostilidad y provocación. Orientado al poder, busca controlar o humillar a otros. Falta de empatía y compasión.',
      prominent: 'Patrón generalizado de comportamiento duro, hostil y dominante. Busca el placer a través del control y la intimidación de los demás, careciendo de empatía y mostrando posible crueldad. Estallidos de ira pueden ser significativos.'
    }
  },
  '7': {
    name: 'Conformista',
    interpretations: {
      low: 'Flexible, espontáneo, menos preocupado por las reglas estrictas.',
      moderate: 'Responsable y cumplidor, puede ser algo rígido y autoexigente. Valora el orden y la previsibilidad.',
      significant: 'Fuerte ambivalencia entre autonomía y sumisión. Reprime sus impulsos y actúa de manera responsable y concienzuda para ganar aprobación, ocultando un enojo subyacente. Tendencias perfeccionistas.',
      prominent: 'Patrón generalizado de conformidad y autodisciplina rígida. Se adhiere estrictamente a reglas internalizadas para controlar la ambivalencia y obtener aprobación, con un fuerte conflicto interno y posible perfeccionismo.'
    }
  },
  '8A': {
    name: 'Resentido',
    interpretations: {
      low: 'Optimista, satisfecho, relaciones estables.',
      moderate: 'Puede sentirse incomprendido o insatisfecho a veces, con cambios de humor y cierta irritabilidad. Dificultad para equilibrar sus necesidades con las de los demás.',
      significant: 'Conflicto entre buscar las recompensas de los demás y sus propios deseos. Se siente confundido, inseguro y resentido. Estados de ánimo impredecibles, irritabilidad y pesimismo.',
      prominent: 'Patrón generalizado de ambivalencia, insatisfacción crónica y resentimiento. Experimenta confusión sobre sí mismo, cambios de humor, irritabilidad y una visión pesimista de la vida, sintiéndose a menudo incomprendido.'
    }
  },
  '8B': {
    name: 'Agraviado',
    interpretations: {
      low: 'Autoestima positiva, optimista, resiliente.',
      moderate: 'Tendencia a la autocrítica y a sentirse menospreciado. Puede albergar dudas sobre sí mismo y mostrarse algo pesimista.',
      significant: 'Actitud auto-descalificadora y auto-denigrante. Se presenta como inferior, evita la atención positiva y sabotea sus propios éxitos. Muchas dudas sobre sí mismo y síntomas ansioso-depresivos.',
      prominent: 'Patrón generalizado de auto-desprecio y victimización. Se considera indigno de elogio, socava sus logros y perpetúa su malestar. Internaliza la angustia, manifestando síntomas crónicos de ansiedad y disforia.'
    }
  },
  '9': {
    name: 'Tendencia Límite',
    interpretations: {
      low: 'Estabilidad emocional, relaciones consistentes, identidad clara.',
      moderate: 'Puede mostrar cierta inestabilidad emocional o en las relaciones, con dificultades para manejar el estrés o los cambios.',
      significant: 'Patrón de personalidad más severo con conflictos internos que contribuyen a una intensa ambivalencia, labilidad emocional, comportamientos impredecibles y difusión de identidad. Dificultad para mantener relaciones positivas y afrontar transiciones vitales.',
      prominent: 'Marcada inestabilidad en el afecto, las relaciones interpersonales y la autoimagen. Conflictos internos generalizados, impulsividad, dificultad para afrontar los desafíos y riesgo de conductas autolesivas o intentos de suicidio.'
    }
  }
};

const EXPRESSED_CONCERNS_INTERPRETATIONS = {
  'A': {
    name: 'Difusión de Identidad',
    interpretations: {
      strength: 'Fuerte sentido de sí mismo, metas claras.',
      low: 'Identidad generalmente estable, puede haber cuestionamientos típicos de la edad.',
      moderate: 'Cierta confusión sobre quién es, sus creencias o su futuro. Puede sentirse algo perdido o menos maduro que sus pares.',
      significant: 'Preocupaciones significativas sobre su identidad. Confusión sobre sus valores, metas y rol en la vida. Se siente perdido y sin rumbo.',
      prominent: 'Grave difusión de identidad. El adolescente se siente profundamente confundido acerca de quién es, lo que quiere y su lugar en el mundo, afectando su capacidad para establecer metas y tomar decisiones.'
    }
  },
  'B': {
    name: 'Infravaloración de uno mismo',
    interpretations: {
      strength: 'Autoestima saludable y confianza en sus capacidades.',
      low: 'Autoestima generalmente adecuada, puede tener dudas ocasionales.',
      moderate: 'Incomodidad con su apariencia, habilidades o estatus social. Cierta insatisfacción general consigo mismo.',
      significant: 'Visión negativa de sí mismo y baja autoestima. Infeliz con aspectos clave de su ser y teme no alcanzar sus metas.',
      prominent: 'Profunda insatisfacción consigo mismo y autoestima muy baja. Se siente inadecuado en múltiples áreas y puede experimentar desesperanza sobre su futuro.'
    }
  },
  'C': {
    name: 'Inseguridad con otros adolescentes',
    interpretations: {
      strength: 'Se siente cómodo y seguro en sus relaciones con pares.',
      low: 'Generalmente se siente aceptado por sus pares, puede experimentar timidez ocasional.',
      moderate: 'Cree que no encaja bien con sus pares. Puede dudar en iniciar contacto por temor al rechazo y sentir que los demás no lo buscan.',
      significant: 'Marcada inseguridad en las relaciones con pares. Dificultad para establecer amistades por miedo al rechazo, sintiéndose a menudo solo y triste.',
      prominent: 'Profunda sensación de no encajar y ser rechazado por sus pares. Evita el contacto social por temor, lo que lleva al aislamiento, la tristeza y la pérdida de experiencias sociales.'
    }
  },
  'D': {
    name: 'Desavenencias familiares',
    interpretations: {
      strength: 'Relaciones familiares percibidas como armoniosas y de apoyo.',
      low: 'Relaciones familiares generalmente buenas, con conflictos ocasionales típicos.',
      moderate: 'Reporta que sus relaciones familiares son tensas y conflictivas. Puede haber un sentimiento de distanciamiento o rebeldía hacia la familia.',
      significant: 'Percepción de un ambiente familiar conflictivo y tenso. Puede expresar rebeldía hacia la estructura y reglas familiares, indicando un malestar significativo.',
      prominent: 'Graves desavenencias familiares. El adolescente percibe su entorno familiar como hostil, conflictivo y carente de apoyo, lo que genera un profundo malestar y posible rebeldía o distanciamiento extremo.'
    }
  }
};

const CLINICAL_SYNDROMES_INTERPRETATIONS = {
  'AA': {
    name: 'Patrones de ingesta por atracón',
    interpretations: {
      low: 'No hay indicios significativos de problemas con la ingesta.',
      significant: 'Presencia de síntomas relacionados con la sobreingesta, atracones o bulimia. Puede haber preocupaciones asociadas con la autoestima, ansiedad o depresión.',
      prominent: 'Patrón de alimentación desordenado prominente. Es probable que el adolescente tenga atracones regulares, posiblemente con conductas compensatorias, y malestar significativo.'
    }
  },
  'BB': {
    name: 'Propensión al abuso de sustancias',
    interpretations: {
      low: 'No hay indicios significativos de abuso de sustancias.',
      significant: 'Admite consumo regular de alcohol y/o drogas, a veces en exceso. El consumo ha superado la experimentación.',
      prominent: 'Fuerte indicación de un problema de abuso de sustancias. El consumo es regular, excesivo y probablemente está interfiriendo con su funcionamiento.'
    }
  },
  'CC': {
    name: 'Predisposición a la delincuencia',
    interpretations: {
      low: 'No hay indicios significativos de conductas delictivas.',
      significant: 'Propensión a patrones de conducta oposicionista-desafiante o disocial. Tiende a romper normas sociales y puede haber tenido problemas con la ley. Impulsividad y poca consideración por los demás.',
      prominent: 'Marcada predisposición a la delincuencia. Comportamiento antisocial, desprecio por las normas, impulsividad y falta de remordimiento son características prominentes.'
    }
  },
  'DD': {
    name: 'Estados de ansiedad',
    interpretations: {
      low: 'No hay indicios significativos de ansiedad generalizada.',
      significant: 'Presencia de múltiples síntomas de ansiedad. Sensación general de aprensión, miedo o inquietud. Preocupaciones excesivas, agitación, y posibles quejas somáticas.',
      prominent: 'Nivel de ansiedad prominente y generalizado. El adolescente experimenta un malestar significativo debido a la aprensión, tensión, preocupaciones excesivas y posibles síntomas físicos, afectando su concentración y sueño.'
    }
  },
  'EE': {
    name: 'Ánimo depresivo',
    interpretations: {
      low: 'No hay indicios significativos de ánimo depresivo.',
      significant: 'Se siente triste y desanimado. Pensamientos negativos sobre el presente y futuro, posible pérdida de interés, cambios en la apariencia, y pérdida de autoconfianza. Puede reportar llanto o fatiga.',
      prominent: 'Estado de ánimo depresivo prominente. Sentimientos intensos de tristeza, desesperanza, pérdida de interés y placer, problemas de autoestima. En niveles más graves, ideación suicida.'
    }
  },
  'FF': {
    name: 'Tendencia suicida',
    interpretations: {
      low: 'No hay indicios significativos de ideación suicida.',
      significant: 'Ha contemplado el suicidio como una opción para terminar con su dolor. Estados depresivos suelen ser severos. Evaluar intencionalidad, plan y acceso.',
      prominent: 'Ideación suicida prominente y significativa. El adolescente considera seriamente el suicidio, sus estados depresivos son graves y puede haber perdido la esperanza. Requiere atención e intervención inmediata.'
    }
  },
  'GG': {
    name: 'Desregulación del estado de ánimo',
    interpretations: {
      low: 'No hay indicios significativos de desregulación disruptiva del ánimo.',
      significant: 'Irritabilidad crónica y problemas de ira de larga data. Explosiones de genio frecuentes y severas, desproporcionadas a la situación, que impactan su funcionamiento.',
      prominent: 'Patrón prominente de desregulación disruptiva del ánimo. Irritabilidad persistente y explosiones de temperamento extremas y frecuentes que causan un deterioro significativo en el hogar, la escuela y con los pares.'
    }
  },
  'HH': {
    name: 'Estrés postraumático',
    interpretations: {
      low: 'No hay indicios significativos de TEPT.',
      significant: 'Reporta haber experimentado o presenciado eventos traumáticos que le han causado angustia significativa. Presencia de pensamientos intrusivos, flashbacks, pesadillas, hipervigilancia y evitación.',
      prominent: 'Síntomas prominentes de estrés postraumático. El adolescente está significativamente afectado por un trauma, con reexperimentación, evitación, hiperactivación y alteraciones negativas en cogniciones y estado de ánimo.'
    }
  },
  'II': {
    name: 'Distorsiones de la realidad',
    interpretations: {
      low: 'No hay indicios significativos de distorsiones de la realidad.',
      significant: 'Experimenta fallos en la prueba de realidad y síntomas psicóticos. Reporta alucinaciones auditivas/visuales, ideación paranoide u otras alteraciones sensoriales/perceptuales. Puede sentirse confundido o asustado. Descartar uso de sustancias.',
      prominent: 'Distorsiones de la realidad prominentes y clínicamente significativas. Presencia clara de síntomas psicóticos que alteran su percepción y pensamiento, causando confusión y miedo. Requiere evaluación psiquiátrica urgente.'
    }
  }
};

function getScoreLevel(pc: number | string, isExpressedConcern: boolean = false): string {
  const numericPc = typeof pc === 'string' ? parseInt(pc) : pc;
  
  if (isExpressedConcern) {
    if (numericPc < 35) return 'strength';
    if (numericPc < 60) return 'low';
    if (numericPc < 75) return 'moderate';
    if (numericPc < 85) return 'significant';
    return 'prominent';
  } else {
    if (numericPc < 60) return 'low';
    if (numericPc < 75) return 'moderate';
    if (numericPc < 85) return 'significant';
    return 'prominent';
  }
}

function calculateAge(birthDate: string): number {
  const birthDateObj = new Date(birthDate);
  const today = new Date();
  let age = today.getFullYear() - birthDateObj.getFullYear();
  const monthDifference = today.getMonth() - birthDateObj.getMonth();
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDateObj.getDate())) {
    age--;
  }
  return age;
}

export function generateQualitativeInterpretation(
  personalityPatterns: ScoreData[],
  expressedConcerns: ScoreData[],
  clinicalSyndromes: ScoreData[],
  validityScales: ScoreData[],
  patientData: PatientData
): string {
  const age = calculateAge(patientData.birth_date);
  const patientName = `${patientData.first_name} ${patientData.last_name}`;
  
  let interpretation = `INTERPRETACIÓN CUALITATIVA DEL MACI-II\n\n`;
  interpretation += `Paciente: ${patientName}\n`;
  interpretation += `Edad: ${age} años\n`;
  interpretation += `Género: ${patientData.gender}\n`;
  interpretation += `Fecha de evaluación: ${new Date().toLocaleDateString('es-ES')}\n\n`;
  
  // Validez del protocolo
  interpretation += `VALIDEZ DEL PROTOCOLO:\n\n`;
  validityScales.forEach(scale => {
    const pc = typeof scale.pc === 'string' ? parseInt(scale.pc) : scale.pc;
    if (scale.scaleId === 'V') {
      interpretation += `Escala V (Validez): PC = ${scale.pc}. `;
      if (pc > 85) {
        interpretation += `Protocolo potencialmente inválido por respuestas inconsistentes o al azar.\n`;
      } else if (pc > 75) {
        interpretation += `Precaución en la interpretación debido a posibles inconsistencias.\n`;
      } else {
        interpretation += `Protocolo válido.\n`;
      }
    } else if (scale.scaleId === 'W') {
      interpretation += `Escala W (Deseabilidad): PC = ${scale.pc}. `;
      if (pc > 85) {
        interpretation += `Tendencia significativa a presentarse de manera excesivamente favorable.\n`;
      } else if (pc > 75) {
        interpretation += `Cierta tendencia a minimizar problemas.\n`;
      } else {
        interpretation += `Sin tendencia significativa a la deseabilidad social.\n`;
      }
    } else if (scale.scaleId === 'X') {
      interpretation += `Escala X (Autodesprecio): PC = ${scale.pc}. `;
      if (pc > 85) {
        interpretation += `Tendencia significativa a exagerar problemas o autodespreciarse.\n`;
      } else if (pc > 75) {
        interpretation += `Cierta tendencia a enfatizar dificultades.\n`;
      } else {
        interpretation += `Sin tendencia significativa al autodesprecio.\n`;
      }
    }
  });
  
  interpretation += `\n`;
  
  // Patrones de Personalidad
  interpretation += `PATRONES DE PERSONALIDAD:\n\n`;
  const elevatedPersonality = personalityPatterns.filter(p => {
    const pc = typeof p.pc === 'string' ? parseInt(p.pc) : p.pc;
    return pc >= 75;
  }).sort((a, b) => {
    const pcA = typeof a.pc === 'string' ? parseInt(a.pc) : a.pc;
    const pcB = typeof b.pc === 'string' ? parseInt(b.pc) : b.pc;
    return pcB - pcA;
  });
  
  if (elevatedPersonality.length === 0) {
    interpretation += `No se identifican patrones de personalidad clínicamente significativos (PC ≥ 75).\n\n`;
  } else {
    interpretation += `Se identifican los siguientes patrones de personalidad clínicamente significativos:\n\n`;
    
    elevatedPersonality.forEach((pattern, index) => {
      const pc = typeof pattern.pc === 'string' ? parseInt(pattern.pc) : pattern.pc;
      const level = getScoreLevel(pc);
      const patternInfo = PERSONALITY_PATTERNS_INTERPRETATIONS[pattern.scaleId as keyof typeof PERSONALITY_PATTERNS_INTERPRETATIONS];
      
      if (patternInfo) {
        interpretation += `${index + 1}. ${patternInfo.name} (PC = ${pattern.pc}):\n`;
        interpretation += `${patternInfo.interpretations[level as keyof typeof patternInfo.interpretations]}\n\n`;
      }
    });
    
    if (elevatedPersonality.length > 1) {
      interpretation += `CONFIGURACIÓN DEL PERFIL:\n`;
      interpretation += `La presencia de múltiples patrones elevados sugiere una personalidad compleja donde ${elevatedPersonality[0].scaleName.toLowerCase()} es el patrón dominante, modificado por características ${elevatedPersonality[1].scaleName.toLowerCase()}s. Esta combinación puede manifestarse en comportamientos que alternan entre ambos estilos dependiendo del contexto y el nivel de estrés.\n\n`;
    }
  }
  
  // Preocupaciones Expresadas
  interpretation += `PREOCUPACIONES EXPRESADAS:\n\n`;
  const significantConcerns = expressedConcerns.filter(c => {
    const pc = typeof c.pc === 'string' ? parseInt(c.pc) : c.pc;
    return pc >= 75 || pc < 35; // Incluir fortalezas (PC < 35)
  });
  
  if (significantConcerns.length === 0) {
    interpretation += `No se identifican preocupaciones expresadas clínicamente significativas.\n\n`;
  } else {
    significantConcerns.forEach(concern => {
      const pc = typeof concern.pc === 'string' ? parseInt(concern.pc) : concern.pc;
      const level = getScoreLevel(pc, true);
      const concernInfo = EXPRESSED_CONCERNS_INTERPRETATIONS[concern.scaleId as keyof typeof EXPRESSED_CONCERNS_INTERPRETATIONS];
      
      if (concernInfo) {
        interpretation += `${concernInfo.name} (PC = ${concern.pc}):\n`;
        interpretation += `${concernInfo.interpretations[level as keyof typeof concernInfo.interpretations]}\n\n`;
      }
    });
  }
  
  // Síndromes Clínicos
  interpretation += `SÍNDROMES CLÍNICOS:\n\n`;
  const elevatedSyndromes = clinicalSyndromes.filter(s => {
    const pc = typeof s.pc === 'string' ? parseInt(s.pc) : s.pc;
    return pc >= 75;
  }).sort((a, b) => {
    const pcA = typeof a.pc === 'string' ? parseInt(a.pc) : a.pc;
    const pcB = typeof b.pc === 'string' ? parseInt(b.pc) : b.pc;
    return pcB - pcA;
  });
  
  if (elevatedSyndromes.length === 0) {
    interpretation += `No se identifican síndromes clínicos significativos (PC ≥ 75).\n\n`;
  } else {
    elevatedSyndromes.forEach(syndrome => {
      const pc = typeof syndrome.pc === 'string' ? parseInt(syndrome.pc) : syndrome.pc;
      const level = pc >= 85 ? 'prominent' : 'significant';
      const syndromeInfo = CLINICAL_SYNDROMES_INTERPRETATIONS[syndrome.scaleId as keyof typeof CLINICAL_SYNDROMES_INTERPRETATIONS];
      
      if (syndromeInfo) {
        interpretation += `${syndromeInfo.name} (PC = ${syndrome.pc}):\n`;
        interpretation += `${syndromeInfo.interpretations[level as keyof typeof syndromeInfo.interpretations]}\n\n`;
        
        // Alertas especiales para síndromes críticos
        if (syndrome.scaleId === 'FF' && pc >= 75) {
          interpretation += `⚠️ ALERTA: Se requiere evaluación inmediata del riesgo suicida y consideración de intervención de crisis.\n\n`;
        }
        if (syndrome.scaleId === 'II' && pc >= 75) {
          interpretation += `⚠️ ALERTA: Se recomienda evaluación psiquiátrica urgente para descartar trastorno psicótico.\n\n`;
        }
      }
    });
  }
  
  // Recomendaciones generales
  interpretation += `RECOMENDACIONES CLÍNICAS:\n\n`;
  
  if (elevatedPersonality.length > 0 || elevatedSyndromes.length > 0) {
    interpretation += `1. Se recomienda evaluación clínica completa para confirmar los hallazgos del MACI-II.\n`;
    interpretation += `2. Considerar intervención psicoterapéutica apropiada para la edad y problemática identificada.\n`;
    
    if (elevatedSyndromes.some(s => ['DD', 'EE', 'FF'].includes(s.scaleId))) {
      interpretation += `3. Monitoreo cercano del estado emocional y riesgo de autolesión.\n`;
    }
    
    if (elevatedSyndromes.some(s => ['BB', 'CC'].includes(s.scaleId))) {
      interpretation += `4. Evaluación de factores de riesgo ambientales y consideración de intervenciones familiares/escolares.\n`;
    }
    
    interpretation += `5. Reevaluación periódica para monitorear progreso y cambios en el funcionamiento.\n`;
  } else {
    interpretation += `Los resultados sugieren un funcionamiento psicológico dentro de límites normales. Se recomienda seguimiento preventivo y apoyo en el desarrollo normal del adolescente.\n`;
  }
  
  interpretation += `\n`;
  interpretation += `NOTA IMPORTANTE: Esta interpretación debe ser integrada con información clínica adicional, historia del desarrollo, contexto familiar y social, y observación clínica directa para una comprensión completa del funcionamiento del adolescente.\n`;
  
  return interpretation;
}