// Interpretación cualitativa basada en los puntajes del MACI-II

interface ScoreData {
  scaleId: string;
  scaleName: string;
  pd: number;
  pc: number | string;
}

interface PatientData {
  first_name: string;
  last_name: string;
  birth_date: string;
  gender: string;
}

// Definiciones de interpretación por escala
const PERSONALITY_PATTERNS_INTERPRETATIONS = {
  '1': {
    name: 'Introvertido',
    interpretations: {
      low: 'Sociable, expresivo, busca interacciones.',
      moderate: 'Tiende a ser reservado y prefiere actividades solitarias, aunque puede interactuar socialmente cuando es necesario. Puede parecer algo distante o indiferente emocionalmente.',
      significant: 'Marcada preferencia por la soledad y el desapego social. Dificultad para experimentar o expresar emociones profundas. Puede tener pocas amistades y dificultades en la comprensión de las sutilezas sociales.',
      prominent: 'Patrón generalizado de desinterés social y emocional. El adolescente es un observador pasivo de la vida, con una capacidad limitada para la calidez o el afecto, y una marcada indiferencia hacia las relaciones.'
    }
  },
  '2': {
    name: 'Inhibido',
    interpretations: {
      low: 'Socialmente seguro, confiado, no teme el rechazo.',
      moderate: 'Cauteloso en situaciones sociales, sensible a la crítica o al rechazo. Puede evitar situaciones nuevas o donde se sienta evaluado.',
      significant: 'Aprensión social significativa. Experimenta malestar y evitación activa de situaciones sociales por temor a la humillación o el rechazo. Baja autoestima y autocrítica.',
      prominent: 'Patrón generalizado de inhibición social, sentimientos de inadecuación e hipersensibilidad a la evaluación negativa. Miedo y desconfianza guían su postura social, con tendencia a la ansiedad y tristeza.'
    }
  },
  '3': {
    name: 'Sumiso',
    interpretations: {
      low: 'Independiente, asertivo, no busca excesivamente la aprobación.',
      moderate: 'Busca agradar a los demás y puede tener dificultades para tomar decisiones por sí mismo. Tiende a depender de otros para obtener apoyo.',
      significant: 'Fuerte necesidad de dependencia y pasividad en las relaciones. Evita el conflicto y subordina sus deseos para mantener el afecto. Falta de autoconfianza e indecisión.',
      prominent: 'Patrón generalizado de comportamiento sumiso y dependiente. Busca activamente relaciones en las que pueda asumir un rol subordinado para asegurar cuidado y afecto. Marcada falta de iniciativa y autonomía.'
    }
  },
  '4': {
    name: 'Dramático',
    interpretations: {
      low: 'Comportamiento y expresión emocional mesurados y estables.',
      moderate: 'Busca atención y puede ser percibido como encantador pero algo superficial. Sus emociones pueden ser intensas pero cambiantes.',
      significant: 'Búsqueda constante de atención y afecto a través de comportamientos encantadores e incluso seductores. Las relaciones tienden a ser superficiales y las emociones lábiles.',
      prominent: 'Patrón generalizado de búsqueda de atención y emocionalidad excesiva. El adolescente necesita ser el centro de atención, utilizando su encanto y a veces la dramatización para lograrlo, con relaciones superficiales y dificultad para desarrollar una identidad cohesiva.'
    }
  },
  '5': {
    name: 'Egocéntrico',
    interpretations: {
      low: 'Humilde, considerado con los demás, empático.',
      moderate: 'Confianza en sí mismo, puede tener altas expectativas sobre sus capacidades y buscar admiración, aunque aún muestra cierta consideración por los demás.',
      significant: 'Autopercepción de superioridad y especial merecimiento. Fuerte deseo de admiración y tendencia a la auto-focalización, lo que dificulta la empatía y puede llevar a la explotación pasiva de otros.',
      prominent: 'Patrón generalizado de grandiosidad, necesidad de admiración y falta de empatía. Se considera especial, espera un trato preferencial y explota a los demás para sus propios fines sin reciprocidad.'
    }
  },
  '6A': {
    name: 'Rebelde',
    interpretations: {
      low: 'Respetuoso de las normas, cooperador, considerado.',
      moderate: 'Cuestiona la autoridad y puede mostrarse algo desafiante o impulsivo. Puede tener dificultades para seguir las reglas consistentemente.',
      significant: 'Percepción del mundo como hostil y desconfianza hacia los demás. Valora la autonomía y actúa para satisfacer sus necesidades sin considerar las reglas sociales o el impacto en otros. Tendencia a la impulsividad.',
      prominent: 'Patrón generalizado de desconfianza, desafío a la autoridad y comportamiento antisocial. Vive el momento, busca gratificación inmediata y muestra poca preocupación por las consecuencias o los sentimientos ajenos, presentando a menudo problemas de conducta.'
    }
  },
  '6B': {
    name: 'Hostil',
    interpretations: {
      low: 'Amable, empático, busca relaciones cooperativas.',
      moderate: 'Puede ser asertivo y algo dominante, con tendencia a imponer su voluntad. Podría mostrar irritabilidad o baja tolerancia a la frustración.',
      significant: 'Fuerte, asertivo y con tendencia a la hostilidad y provocación. Orientado al poder, busca controlar o humillar a otros. Falta de empatía y compasión.',
      prominent: 'Patrón generalizado de comportamiento duro, hostil y dominante. Busca el placer a través del control y la intimidación de los demás, careciendo de empatía y mostrando posible crueldad. Estallidos de ira pueden ser significativos.'
    }
  },
  '7': {
    name: 'Conformista',
    interpretations: {
      low: 'Flexible, espontáneo, menos preocupado por las reglas estrictas.',
      moderate: 'Responsable y cumplidor, puede ser algo rígido y autoexigente. Valora el orden y la previsibilidad.',
      significant: 'Fuerte ambivalencia entre autonomía y sumisión. Reprime sus impulsos y actúa de manera responsable y concienzuda para ganar aprobación, ocultando un enojo subyacente. Tendencias perfeccionistas.',
      prominent: 'Patrón generalizado de conformidad y autodisciplina rígida. Se adhiere estrictamente a reglas internalizadas para controlar la ambivalencia y obtener aprobación, con un fuerte conflicto interno y posible perfeccionismo.'
    }
  },
  '8A': {
    name: 'Resentido',
    interpretations: {
      low: 'Optimista, satisfecho, relaciones estables.',
      moderate: 'Puede sentirse incomprendido o insatisfecho a veces, con cambios de humor y cierta irritabilidad. Dificultad para equilibrar sus necesidades con las de los demás.',
      significant: 'Conflicto entre buscar las recompensas de los demás y sus propios deseos. Se siente confundido, inseguro y resentido. Estados de ánimo impredecibles, irritabilidad y pesimismo.',
      prominent: 'Patrón generalizado de ambivalencia, insatisfacción crónica y resentimiento. Experimenta confusión sobre sí mismo, cambios de humor, irritabilidad y una visión pesimista de la vida, sintiéndose a menudo incomprendido.'
    }
  },
  '8B': {
    name: 'Agraviado',
    interpretations: {
      low: 'Autoestima positiva, optimista, resiliente.',
      moderate: 'Tendencia a la autocrítica y a sentirse menospreciado. Puede albergar dudas sobre sí mismo y mostrarse algo pesimista.',
      significant: 'Actitud auto-descalificadora y auto-denigrante. Se presenta como inferior, evita la atención positiva y sabotea sus propios éxitos. Muchas dudas sobre sí mismo y síntomas ansioso-depresivos.',
      prominent: 'Patrón generalizado de auto-desprecio y victimización. Se considera indigno de elogio, socava sus logros y perpetúa su malestar. Internaliza la angustia, manifestando síntomas crónicos de ansiedad y disforia.'
    }
  },
  '9': {
    name: 'Tendencia Límite',
    interpretations: {
      low: 'Estabilidad emocional, relaciones consistentes, identidad clara.',
      moderate: 'Puede mostrar cierta inestabilidad emocional o en las relaciones, con dificultades para manejar el estrés o los cambios.',
      significant: 'Patrón de personalidad más severo con conflictos internos que contribuyen a una intensa ambivalencia, labilidad emocional, comportamientos impredecibles y difusión de identidad. Dificultad para mantener relaciones positivas y afrontar transiciones vitales.',
      prominent: 'Marcada inestabilidad en el afecto, las relaciones interpersonales y la autoimagen. Conflictos internos generalizados, impulsividad, dificultad para afrontar los desafíos y riesgo de conductas autolesivas o intentos de suicidio.'
    }
  }
};

const EXPRESSED_CONCERNS_INTERPRETATIONS = {
  'A': {
    name: 'Difusión de Identidad',
    interpretations: {
      strength: 'Fuerte sentido de sí mismo, metas claras.',
      low: 'Identidad generalmente estable, puede haber cuestionamientos típicos de la edad.',
      moderate: 'Cierta confusión sobre quién es, sus creencias o su futuro. Puede sentirse algo perdido o menos maduro que sus pares.',
      significant: 'Preocupaciones significativas sobre su identidad. Confusión sobre sus valores, metas y rol en la vida. Se siente perdido y sin rumbo.',
      prominent: 'Grave difusión de identidad. El adolescente se siente profundamente confundido acerca de quién es, lo que quiere y su lugar en el mundo, afectando su capacidad para establecer metas y tomar decisiones.'
    }
  },
  'B': {
    name: 'Infravaloración de uno mismo',
    interpretations: {
      strength: 'Autoestima saludable y confianza en sus capacidades.',
      low: 'Autoestima generalmente adecuada, puede tener dudas ocasionales.',
      moderate: 'Incomodidad con su apariencia, habilidades o estatus social. Cierta insatisfacción general consigo mismo.',
      significant: 'Visión negativa de sí mismo y baja autoestima. Infeliz con aspectos clave de su ser y teme no alcanzar sus metas.',
      prominent: 'Profunda insatisfacción consigo mismo y autoestima muy baja. Se siente inadecuado en múltiples áreas y puede experimentar desesperanza sobre su futuro.'
    }
  },
  'C': {
    name: 'Inseguridad con otros adolescentes',
    interpretations: {
      strength: 'Se siente cómodo y seguro en sus relaciones con pares.',
      low: 'Generalmente se siente aceptado por sus pares, puede experimentar timidez ocasional.',
      moderate: 'Cree que no encaja bien con sus pares. Puede dudar en iniciar contacto por temor al rechazo y sentir que los demás no lo buscan.',
      significant: 'Marcada inseguridad en las relaciones con pares. Dificultad para establecer amistades por miedo al rechazo, sintiéndose a menudo solo y triste.',
      prominent: 'Profunda sensación de no encajar y ser rechazado por sus pares. Evita el contacto social por temor, lo que lleva al aislamiento, la tristeza y la pérdida de experiencias sociales.'
    }
  },
  'D': {
    name: 'Desavenencias familiares',
    interpretations: {
      strength: 'Relaciones familiares percibidas como armoniosas y de apoyo.',
      low: 'Relaciones familiares generalmente buenas, con conflictos ocasionales típicos.',
      moderate: 'Reporta que sus relaciones familiares son tensas y conflictivas. Puede haber un sentimiento de distanciamiento o rebeldía hacia la familia.',
      significant: 'Percepción de un ambiente familiar conflictivo y tenso. Puede expresar rebeldía hacia la estructura y reglas familiares, indicando un malestar significativo.',
      prominent: 'Graves desavenencias familiares. El adolescente percibe su entorno familiar como hostil, conflictivo y carente de apoyo, lo que genera un profundo malestar y posible rebeldía o distanciamiento extremo.'
    }
  }
};

const CLINICAL_SYNDROMES_INTERPRETATIONS = {
  'AA': {
    name: 'Patrones de ingesta por atracón',
    interpretations: {
      low: 'No hay indicios significativos de problemas con la ingesta.',
      significant: 'Presencia de síntomas relacionados con la sobreingesta, atracones o bulimia. Puede haber preocupaciones asociadas con la autoestima, ansiedad o depresión.',
      prominent: 'Patrón de alimentación desordenado prominente. Es probable que el adolescente tenga atracones regulares, posiblemente con conductas compensatorias, y malestar significativo.'
    }
  },
  'BB': {
    name: 'Propensión al abuso de sustancias',
    interpretations: {
      low: 'No hay indicios significativos de abuso de sustancias.',
      significant: 'Admite consumo regular de alcohol y/o drogas, a veces en exceso. El consumo ha superado la experimentación.',
      prominent: 'Fuerte indicación de un problema de abuso de sustancias. El consumo es regular, excesivo y probablemente está interfiriendo con su funcionamiento.'
    }
  },
  'CC': {
    name: 'Predisposición a la delincuencia',
    interpretations: {
      low: 'No hay indicios significativos de conductas delictivas.',
      significant: 'Propensión a patrones de conducta oposicionista-desafiante o disocial. Tiende a romper normas sociales y puede haber tenido problemas con la ley. Impulsividad y poca consideración por los demás.',
      prominent: 'Marcada predisposición a la delincuencia. Comportamiento antisocial, desprecio por las normas, impulsividad y falta de remordimiento son características prominentes.'
    }
  },
  'DD': {
    name: 'Estados de ansiedad',
    interpretations: {
      low: 'No hay indicios significativos de ansiedad generalizada.',
      significant: 'Presencia de múltiples síntomas de ansiedad. Sensación general de aprensión, miedo o inquietud. Preocupaciones excesivas, agitación, y posibles quejas somáticas.',
      prominent: 'Nivel de ansiedad prominente y generalizado. El adolescente experimenta un malestar significativo debido a la aprensión, tensión, preocupaciones excesivas y posibles síntomas físicos, afectando su concentración y sueño.'
    }
  },
  'EE': {
    name: 'Ánimo depresivo',
    interpretations: {
      low: 'No hay indicios significativos de ánimo depresivo.',
      significant: 'Se siente triste y desanimado. Pensamientos negativos sobre el presente y futuro, posible pérdida de interés, cambios en la apariencia, y pérdida de autoconfianza. Puede reportar llanto o fatiga.',
      prominent: 'Estado de ánimo depresivo prominente. Sentimientos intensos de tristeza, desesperanza, pérdida de interés y placer, problemas de autoestima. En niveles más graves, ideación suicida.'
    }
  },
  'FF': {
    name: 'Tendencia suicida',
    interpretations: {
      low: 'No hay indicios significativos de ideación suicida.',
      significant: 'Ha contemplado el suicidio como una opción para terminar con su dolor. Estados depresivos suelen ser severos. Evaluar intencionalidad, plan y acceso.',
      prominent: 'Ideación suicida prominente y significativa. El adolescente considera seriamente el suicidio, sus estados depresivos son graves y puede haber perdido la esperanza. Requiere atención e intervención inmediata.'
    }
  },
  'GG': {
    name: 'Desregulación del estado de ánimo',
    interpretations: {
      low: 'No hay indicios significativos de desregulación disruptiva del ánimo.',
      significant: 'Irritabilidad crónica y problemas de ira de larga data. Explosiones de genio frecuentes y severas, desproporcionadas a la situación, que impactan su funcionamiento.',
      prominent: 'Patrón prominente de desregulación disruptiva del ánimo. Irritabilidad persistente y explosiones de temperamento extremas y frecuentes que causan un deterioro significativo en el hogar, la escuela y con los pares.'
    }
  },
  'HH': {
    name: 'Estrés postraumático',
    interpretations: {
      low: 'No hay indicios significativos de TEPT.',
      significant: 'Reporta haber experimentado o presenciado eventos traumáticos que le han causado angustia significativa. Presencia de pensamientos intrusivos, flashbacks, pesadillas, hipervigilancia y evitación.',
      prominent: 'Síntomas prominentes de estrés postraumático. El adolescente está significativamente afectado por un trauma, con reexperimentación, evitación, hiperactivación y alteraciones negativas en cogniciones y estado de ánimo.'
    }
  },
  'II': {
    name: 'Distorsiones de la realidad',
    interpretations: {
      low: 'No hay indicios significativos de distorsiones de la realidad.',
      significant: 'Experimenta fallos en la prueba de realidad y síntomas psicóticos. Reporta alucinaciones auditivas/visuales, ideación paranoide u otras alteraciones sensoriales/perceptuales. Puede sentirse confundido o asustado. Descartar uso de sustancias.',
      prominent: 'Distorsiones de la realidad prominentes y clínicamente significativas. Presencia clara de síntomas psicóticos que alteran su percepción y pensamiento, causando confusión y miedo. Requiere evaluación psiquiátrica urgente.'
    }
  }
};

function getScoreLevel(pc: number | string, isExpressedConcern: boolean = false): string {
  const numericPc = typeof pc === 'string' ? parseInt(pc) : pc;
  
  if (isExpressedConcern) {
    if (numericPc < 35) return 'strength';
    if (numericPc < 60) return 'low';
    if (numericPc < 75) return 'moderate';
    if (numericPc < 85) return 'significant';
    return 'prominent';
  } else {
    if (numericPc < 60) return 'low';
    if (numericPc < 75) return 'moderate';
    if (numericPc < 85) return 'significant';
    return 'prominent';
  }
}

function calculateAge(birthDate: string): number {
  const birthDateObj = new Date(birthDate);
  const today = new Date();
  let age = today.getFullYear() - birthDateObj.getFullYear();
  const monthDifference = today.getMonth() - birthDateObj.getMonth();
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDateObj.getDate())) {
    age--;
  }
  return age;
}

export function generateQualitativeInterpretation(
  personalityPatterns: ScoreData[],
  expressedConcerns: ScoreData[],
  clinicalSyndromes: ScoreData[],
  validityScales: ScoreData[],
  patientData: PatientData
): string {
  // Validar que los datos del paciente estén completos
  if (!patientData.birth_date || !patientData.first_name || !patientData.last_name) {
    throw new Error('Datos del paciente incompletos para generar la interpretación');
  }
  
  const age = calculateAge(patientData.birth_date);
  const patientName = `${patientData.first_name} ${patientData.last_name}`;

  // Ensure all score inputs are arrays
  const personalityPatternsArray = Array.isArray(personalityPatterns) ? personalityPatterns : Object.values(personalityPatterns);
  const expressedConcernsArray = Array.isArray(expressedConcerns) ? expressedConcerns : Object.values(expressedConcerns);
  const clinicalSyndromesArray = Array.isArray(clinicalSyndromes) ? clinicalSyndromes : Object.values(clinicalSyndromes);
  const validityScalesArray = Array.isArray(validityScales) ? validityScales : Object.values(validityScales);
  
  let interpretation = `INTERPRETACIÓN CUALITATIVA DEL MACI-II\n\n`;
  interpretation += `Paciente: ${patientName}\n`;
  interpretation += `Edad: ${age} años\n`;
  interpretation += `Género: ${patientData.gender === 'M' ? 'Masculino' : 'Femenino'}\n`;
  interpretation += `Fecha de evaluación: ${new Date().toLocaleDateString('es-ES')}\n\n`;

  // Validez del protocolo
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                                VALIDEZ DEL PROTOCOLO\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;

  const validityIssues = [];
  validityScalesArray.forEach(scale => {
    const pc = typeof scale.pc === 'string' ? parseInt(scale.pc) : scale.pc;
    if (scale.scaleId === 'X') {
      interpretation += `• Transparencia (X): PC = ${scale.pc} - `;
      if (pc > 85) {
        interpretation += `Elevada. Posible tendencia a presentarse de manera excesivamente favorable o negación de problemas.\n`;
        validityIssues.push('Transparencia elevada');
      } else if (pc > 75) {
        interpretation += `Moderadamente elevada. Cierta tendencia a minimizar dificultades.\n`;
      } else {
        interpretation += `Dentro de límites normales.\n`;
      }
    } else if (scale.scaleId === 'Y') {
      interpretation += `• Deseabilidad (Y): PC = ${scale.pc} - `;
      if (pc > 85) {
        interpretation += `Elevada. Intento significativo de presentarse de manera socialmente deseable.\n`;
        validityIssues.push('Deseabilidad elevada');
      } else if (pc > 75) {
        interpretation += `Moderadamente elevada. Cierta tendencia a la deseabilidad social.\n`;
      } else {
        interpretation += `Dentro de límites normales.\n`;
      }
    } else if (scale.scaleId === 'Z') {
      interpretation += `• Alteración (Z): PC = ${scale.pc} - `;
      if (pc > 85) {
        interpretation += `Elevada. Posible exageración de síntomas o "grito de ayuda".\n`;
        validityIssues.push('Alteración elevada');
      } else if (pc > 75) {
        interpretation += `Moderadamente elevada. Cierta tendencia a enfatizar dificultades.\n`;
      } else {
        interpretation += `Sin tendencia significativa al autodesprecio.\n`;
      }
    }
  });
  
  if (validityIssues.length === 0) {
    interpretation += `\n✓ CONCLUSIÓN: El protocolo presenta indicadores de validez dentro de límites aceptables, sugiriendo que las respuestas fueron dadas de manera cooperativa y honesta.\n\n`;
  } else {
    interpretation += `\n⚠️  CONSIDERACIONES: Las elevaciones en validez requieren cautela en la interpretación de los resultados.\n\n`;
  }

  // Patrones de Personalidad
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                              PATRONES DE PERSONALIDAD\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;

  const elevatedPersonality = personalityPatternsArray.filter(p => {
    const pc = typeof p.pc === 'string' ? parseInt(p.pc) : p.pc;
    return pc >= 75;
  }).sort((a, b) => {
    const pcA = typeof a.pc === 'string' ? parseInt(a.pc) : a.pc;
    const pcB = typeof b.pc === 'string' ? parseInt(b.pc) : b.pc;
    return pcB - pcA;
  });

  // Mostrar todos los patrones con interpretación detallada
  const getDetailedPersonalityInterpretation = (scaleId: string, pc: number, scaleName: string) => {
    let interp = '';

    switch(scaleId) {
      case '1': // Introvertido
        if (pc >= 85) {
          interp = `Patrón generalizado de desinterés social y emocional. ${scaleName} es un observador pasivo de la vida, con una capacidad limitada para la calidez o el afecto, y una marcada indiferencia hacia las relaciones.`;
        } else if (pc >= 75) {
          interp = `Marcada preferencia por la soledad y el desapego social. Dificultad para experimentar o expresar emociones profundas. Puede tener pocas amistades y dificultades en la comprensión de las sutilezas sociales.`;
        } else if (pc >= 60) {
          interp = `Tiende a ser reservado y prefiere actividades solitarias, aunque puede interactuar socialmente cuando es necesario. Puede parecer algo distante o indiferente emocionalmente.`;
        }
        break;

      case '4': // Dramático
        if (pc >= 85) {
          interp = `Patrón generalizado de búsqueda de atención y emocionalidad excesiva. Necesita ser el centro de atención, utilizando su encanto y a veces la dramatización para lograrlo, con relaciones superficiales y dificultad para desarrollar una identidad cohesiva.`;
        } else if (pc >= 75) {
          interp = `Búsqueda constante de atención y afecto a través de comportamientos encantadores e incluso seductores. Las relaciones tienden a ser superficiales y las emociones lábiles.`;
        } else if (pc >= 60) {
          interp = `Busca atención y puede ser percibido como encantador pero algo superficial. Sus emociones pueden ser intensas pero cambiantes.`;
        }
        break;

      case '9': // Tendencia Límite
        if (pc >= 85) {
          interp = `Marcada inestabilidad en el afecto, las relaciones interpersonales y la autoimagen. Conflictos internos generalizados, impulsividad, dificultad para afrontar los desafíos y riesgo de conductas autolesivas o intentos de suicidio.`;
        } else if (pc >= 75) {
          interp = `Patrón de personalidad más severo con conflictos internos que contribuyen a una intensa ambivalencia, labilidad emocional, comportamientos impredecibles y difusión de identidad. Dificultad para mantener relaciones positivas y afrontar transiciones vitales.`;
        } else if (pc >= 60) {
          interp = `Puede mostrar cierta inestabilidad emocional o en las relaciones, con dificultades para manejar el estrés o los cambios.`;
        }
        break;

      default:
        if (pc >= 85) {
          interp = `Patrón de personalidad prominente y generalizado que afecta significativamente el funcionamiento diario y las relaciones interpersonales.`;
        } else if (pc >= 75) {
          interp = `Patrón de personalidad clínicamente significativo que probablemente causa dificultades en el funcionamiento del adolescente.`;
        } else if (pc >= 60) {
          interp = `Presencia de algunos rasgos del estilo de personalidad, que pueden ser evidentes bajo estrés.`;
        }
    }

    return interp;
  };

  if (elevatedPersonality.length === 0) {
    interpretation += `No se identifican patrones de personalidad clínicamente significativos (PC ≥ 75).\n\n`;
    interpretation += `Los puntajes sugieren un perfil de personalidad dentro de límites adaptativos, sin patrones disfuncionales prominentes.\n\n`;
  } else {
    interpretation += `Se identifican los siguientes patrones de personalidad clínicamente significativos:\n\n`;

    elevatedPersonality.forEach((pattern, index) => {
      const pc = typeof pattern.pc === 'string' ? parseInt(pattern.pc) : pattern.pc;
      const severity = pc >= 85 ? 'PROMINENTE' : 'SIGNIFICATIVO';

      interpretation += `${index + 1}. ${pattern.scaleName} (Escala ${pattern.scaleId}) - PC = ${pattern.pc} [${severity}]\n\n`;
      interpretation += `${getDetailedPersonalityInterpretation(pattern.scaleId, pc, pattern.scaleName)}\n\n`;
    });
    
    if (elevatedPersonality.length > 1) {
      interpretation += `🔄 CONFIGURACIÓN DEL PERFIL:\n`;
      interpretation += `La presencia de múltiples patrones elevados sugiere una personalidad compleja donde "${elevatedPersonality[0].scaleName}" es el patrón dominante, modificado por características de "${elevatedPersonality[1].scaleName}". Esta combinación puede manifestarse en comportamientos que alternan entre ambos estilos dependiendo del contexto y el nivel de estrés.\n\n`;
    }
  }

  // Preocupaciones Expresadas
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                            PREOCUPACIONES EXPRESADAS\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;

  const getDetailedConcernInterpretation = (scaleId: string, pc: number, scaleName: string) => {
    let interp = '';

    switch(scaleId) {
      case 'A': // Difusión de Identidad
        if (pc >= 85) {
          interp = `Grave difusión de identidad. Se siente profundamente confundido acerca de quién es, lo que quiere y su lugar en el mundo, afectando su capacidad para establecer metas y tomar decisiones.`;
        } else if (pc >= 75) {
          interp = `Preocupaciones significativas sobre su identidad. Confusión sobre sus valores, metas y rol en la vida. Se siente perdido y sin rumbo.`;
        } else if (pc >= 60) {
          interp = `Cierta confusión sobre quién es, sus creencias o su futuro. Puede sentirse algo perdido o menos maduro que sus pares.`;
        } else if (pc < 35) {
          interp = `FORTALEZA: Fuerte sentido de sí mismo, metas claras y identidad bien establecida.`;
        }
        break;

      case 'D': // Desavenencias Familiares
        if (pc >= 85) {
          interp = `Graves desavenencias familiares. Percibe su entorno familiar como hostil, conflictivo y carente de apoyo, lo que genera un profundo malestar y posible rebeldía o distanciamiento extremo.`;
        } else if (pc >= 75) {
          interp = `Percepción de un ambiente familiar conflictivo y tenso. Puede expresar rebeldía hacia la estructura y reglas familiares, indicando un malestar significativo.`;
        } else if (pc >= 60) {
          interp = `Reporta que sus relaciones familiares son tensas y conflictivas. Puede haber un sentimiento de distanciamiento o rebeldía hacia la familia.`;
        } else if (pc < 35) {
          interp = `FORTALEZA: Relaciones familiares percibidas como armoniosas y de apoyo.`;
        }
        break;

      default:
        if (pc >= 85) {
          interp = `Preocupación prominente que genera malestar significativo y afecta el funcionamiento diario.`;
        } else if (pc >= 75) {
          interp = `Preocupación clínicamente significativa que requiere atención.`;
        } else if (pc >= 60) {
          interp = `Preocupación moderada que puede ser evidente bajo estrés.`;
        } else if (pc < 35) {
          interp = `FORTALEZA: Área de adaptación y funcionamiento positivo.`;
        }
    }

    return interp;
  };

  const significantConcerns = expressedConcernsArray.filter(c => {
    const pc = typeof c.pc === 'string' ? parseInt(c.pc) : c.pc;
    return pc >= 60 || pc < 35; // Incluir preocupaciones moderadas y fortalezas
  }).sort((a, b) => {
    const pcA = typeof a.pc === 'string' ? parseInt(a.pc) : a.pc;
    const pcB = typeof b.pc === 'string' ? parseInt(b.pc) : b.pc;
    return pcB - pcA;
  });

  if (significantConcerns.length === 0) {
    interpretation += `No se identifican preocupaciones expresadas clínicamente significativas ni áreas de fortaleza destacables.\n\n`;
  } else {
    significantConcerns.forEach((concern, index) => {
      const pc = typeof concern.pc === 'string' ? parseInt(concern.pc) : concern.pc;
      const isStrength = pc < 35;
      const severity = pc >= 85 ? 'PROMINENTE' : pc >= 75 ? 'SIGNIFICATIVA' : pc >= 60 ? 'MODERADA' : 'FORTALEZA';

      interpretation += `${index + 1}. ${concern.scaleName} (Escala ${concern.scaleId}) - PC = ${concern.pc} [${severity}]\n\n`;
      interpretation += `${getDetailedConcernInterpretation(concern.scaleId, pc, concern.scaleName)}\n\n`;
    });
  }
  
  // Síndromes Clínicos
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                               SÍNDROMES CLÍNICOS\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;

  const getDetailedSyndromeInterpretation = (scaleId: string, pc: number, scaleName: string) => {
    let interp = '';

    switch(scaleId) {
      case 'FF': // Tendencia Suicida
        if (pc >= 85) {
          interp = `Ideación suicida prominente y significativa. Considera seriamente el suicidio, sus estados depresivos son graves y puede haber perdido la esperanza. REQUIERE ATENCIÓN E INTERVENCIÓN INMEDIATA.`;
        } else if (pc >= 75) {
          interp = `Ha contemplado el suicidio como una opción para terminar con su dolor. Estados depresivos suelen ser severos. Evaluar intencionalidad, plan y acceso.`;
        }
        break;

      case 'DD': // Estados de Ansiedad
        if (pc >= 85) {
          interp = `Nivel de ansiedad prominente y generalizado. Experimenta un malestar significativo debido a la aprensión, tensión, preocupaciones excesivas y posibles síntomas físicos, afectando su concentración y sueño.`;
        } else if (pc >= 75) {
          interp = `Presencia de múltiples síntomas de ansiedad. Sensación general de aprensión, miedo o inquietud. Preocupaciones excesivas, agitación, y posibles quejas somáticas.`;
        }
        break;

      case 'EE': // Afecto Depresivo
        if (pc >= 85) {
          interp = `Síntomas depresivos prominentes y generalizados. Estado de ánimo persistentemente bajo, desesperanza, pérdida de interés en actividades, fatiga y posibles alteraciones del sueño y apetito.`;
        } else if (pc >= 75) {
          interp = `Presencia de síntomas depresivos significativos. Estado de ánimo bajo, sentimientos de tristeza, desesperanza y pérdida de interés en actividades previamente placenteras.`;
        }
        break;

      case 'CC': // Predisposición a la Delincuencia
        if (pc >= 85) {
          interp = `Marcada predisposición a la delincuencia. Comportamiento antisocial, desprecio por las normas, impulsividad y falta de remordimiento son características prominentes.`;
        } else if (pc >= 75) {
          interp = `Propensión a patrones de conducta oposicionista-desafiante o disocial. Tiende a romper normas sociales y puede haber tenido problemas con la ley. Impulsividad y poca consideración por los demás.`;
        }
        break;

      case 'HH': // Estrés Postraumático
        if (pc >= 85) {
          interp = `Síntomas prominentes de estrés postraumático. Está significativamente afectado por un trauma, con reexperimentación, evitación, hiperactivación y alteraciones negativas en cogniciones y estado de ánimo.`;
        } else if (pc >= 75) {
          interp = `Reporta haber experimentado o presenciado eventos traumáticos que le han causado angustia significativa. Presencia de pensamientos intrusivos, flashbacks, pesadillas, hipervigilancia y evitación.`;
        }
        break;

      case 'II': // Distorsiones de la Realidad
        if (pc >= 85) {
          interp = `Distorsiones de la realidad prominentes y clínicamente significativas. Presencia clara de síntomas psicóticos que alteran su percepción y pensamiento, causando confusión y miedo. REQUIERE EVALUACIÓN PSIQUIÁTRICA URGENTE.`;
        } else if (pc >= 75) {
          interp = `Experimenta fallos en la prueba de realidad y síntomas psicóticos. Reporta alucinaciones auditivas/visuales, ideación paranoide u otras alteraciones sensoriales/perceptuales. Puede sentirse confundido o asustado. Descartar uso de sustancias.`;
        }
        break;

      default:
        if (pc >= 85) {
          interp = `Síndrome clínico prominente que afecta significativamente el funcionamiento diario y requiere intervención especializada.`;
        } else if (pc >= 75) {
          interp = `Síndrome clínico presente que requiere atención y seguimiento profesional.`;
        }
    }

    return interp;
  };

  const elevatedSyndromes = clinicalSyndromesArray.filter(s => {
    const pc = typeof s.pc === 'string' ? parseInt(s.pc) : s.pc;
    return pc >= 75;
  }).sort((a, b) => {
    const pcA = typeof a.pc === 'string' ? parseInt(a.pc) : a.pc;
    const pcB = typeof b.pc === 'string' ? parseInt(b.pc) : b.pc;
    return pcB - pcA;
  });

  if (elevatedSyndromes.length === 0) {
    interpretation += `No se identifican síndromes clínicos significativos (PC ≥ 75).\n\n`;
    interpretation += `Los puntajes sugieren ausencia de psicopatología clínica prominente en las áreas evaluadas.\n\n`;
  } else {
    interpretation += `Se identifican los siguientes síndromes clínicos:\n\n`;

    elevatedSyndromes.forEach((syndrome, index) => {
      const pc = typeof syndrome.pc === 'string' ? parseInt(syndrome.pc) : syndrome.pc;
      const severity = pc >= 85 ? 'PROMINENTE' : 'SIGNIFICATIVO';

      interpretation += `${index + 1}. ${syndrome.scaleName} (Escala ${syndrome.scaleId}) - PC = ${syndrome.pc} [${severity}]\n\n`;
      interpretation += `${getDetailedSyndromeInterpretation(syndrome.scaleId, pc, syndrome.scaleName)}\n\n`;

      // Alertas especiales para síndromes críticos
      if (syndrome.scaleId === 'FF' && pc >= 75) {
        interpretation += `*** ALERTA CRÍTICA: Se requiere evaluación inmediata del riesgo suicida y consideración de intervención de crisis. ***\n\n`;
      }
      if (syndrome.scaleId === 'II' && pc >= 75) {
        interpretation += `🚨 ALERTA CRÍTICA: Se recomienda evaluación psiquiátrica urgente para descartar trastorno psicótico.\n\n`;
      }
    });
  }
  
  // Recomendaciones generales
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                            RECOMENDACIONES CLÍNICAS\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;

  if (elevatedPersonality.length > 0 || elevatedSyndromes.length > 0) {
    interpretation += `📋 RECOMENDACIONES GENERALES:\n\n`;
    interpretation += `1. 🔍 EVALUACIÓN CLÍNICA COMPLETA: Se recomienda evaluación psicológica/psiquiátrica integral para confirmar los hallazgos del MACI-II e integrar con otras fuentes de información.\n\n`;
    interpretation += `2. 🎯 INTERVENCIÓN PSICOTERAPÉUTICA: Considerar psicoterapia apropiada para la edad y problemática identificada (terapia cognitivo-conductual, terapia dialéctica conductual, terapia familiar, etc.).\n\n`;

    // Recomendaciones específicas basadas en síndromes
    if (elevatedSyndromes.some(s => s.scaleId === 'FF')) {
      interpretation += `3. 🚨 EVALUACIÓN DE RIESGO SUICIDA: Implementar protocolo de evaluación de riesgo suicida inmediato. Considerar hospitalización si hay riesgo inminente.\n\n`;
    }

    if (elevatedSyndromes.some(s => ['DD', 'EE'].includes(s.scaleId))) {
      interpretation += `4. 💊 EVALUACIÓN PSIQUIÁTRICA: Considerar evaluación para posible tratamiento farmacológico de síntomas ansiosos y/o depresivos.\n\n`;
    }

    if (elevatedSyndromes.some(s => s.scaleId === 'II')) {
      interpretation += `5. 🧠 EVALUACIÓN NEUROPSICOLÓGICA: Realizar evaluación neuropsicológica y considerar estudios de neuroimagen si se sospecha patología orgánica.\n\n`;
    }

    if (elevatedSyndromes.some(s => ['BB', 'CC'].includes(s.scaleId))) {
      interpretation += `6. 🏠 INTERVENCIÓN SISTÉMICA: Evaluación de factores de riesgo ambientales, familiares y escolares. Considerar terapia familiar y coordinación con instituciones educativas.\n\n`;
    }

    if (significantConcerns.some(c => c.scaleId === 'D' && (typeof c.pc === 'string' ? parseInt(c.pc) : c.pc) >= 75)) {
      interpretation += `7. 👨‍👩‍👧‍👦 TERAPIA FAMILIAR: Abordar las dinámicas familiares conflictivas identificadas a través de terapia familiar sistémica.\n\n`;
    }

    interpretation += `8. 📊 SEGUIMIENTO Y REEVALUACIÓN: Monitoreo periódico del progreso terapéutico y reevaluación con instrumentos estandarizados cada 6-12 meses.\n\n`;
    interpretation += `9. 🎓 COORDINACIÓN ESCOLAR: Comunicación con el equipo educativo para implementar adaptaciones académicas si es necesario.\n\n`;
    interpretation += `10. 👥 APOYO PSICOSOCIAL: Facilitar acceso a grupos de apoyo apropiados para la edad y problemática identificada.\n\n`;

  } else {
    interpretation += `✅ FUNCIONAMIENTO ADAPTATIVO:\n\n`;
    interpretation += `Los resultados sugieren un funcionamiento psicológico dentro de límites adaptativos. Se recomienda:\n\n`;
    interpretation += `• Seguimiento preventivo y apoyo en el desarrollo normal del adolescente\n`;
    interpretation += `• Fortalecimiento de factores protectores identificados\n`;
    interpretation += `• Reevaluación en caso de cambios significativos en el funcionamiento\n\n`;
  }

  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n`;
  interpretation += `                                CONSIDERACIONES FINALES\n`;
  interpretation += `═══════════════════════════════════════════════════════════════════════════════\n\n`;
  interpretation += `⚠️  IMPORTANTE: Esta interpretación debe ser integrada con:\n`;
  interpretation += `• Historia clínica y del desarrollo completa\n`;
  interpretation += `• Entrevista clínica estructurada\n`;
  interpretation += `• Información de múltiples fuentes (familia, escuela, pares)\n`;
  interpretation += `• Observación clínica directa del comportamiento\n`;
  interpretation += `• Contexto sociocultural y familiar específico\n`;
  interpretation += `• Otros instrumentos de evaluación psicológica\n\n`;
  interpretation += `NOTA IMPORTANTE: El MACI-II es una herramienta de screening y debe complementarse con una evaluación clínica integral para establecer diagnósticos y planes de tratamiento definitivos.\n\n`;
  interpretation += `Interpretación realizada por: [Nombre del profesional]\n`;
  interpretation += `Fecha del informe: ${new Date().toLocaleDateString('es-ES')}\n`;
  
  return interpretation;
}