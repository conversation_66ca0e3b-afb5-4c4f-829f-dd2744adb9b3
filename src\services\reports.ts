import { supabase } from '../config/supabase';
import { TestScores } from '../types/scores'
import { ApiResponse } from '../types/common'

export interface Report {
  id: string;
  patient_id: string;
  response_id: string;
  title: string;
  content: string;
  qualitative_interpretation: string;
  created_at: string;
  updated_at: string;
  patient?: {
    first_name: string;
    last_name: string;
    birth_date: string;
    gender: string;
  };
}

export interface CreateReportData {
  patient_id: string;
  response_id: string;
  title: string;
  content: string;
  qualitative_interpretation: string;
}

class ReportsService {
  // Función para verificar si la tabla existe
  async ensureTableExists(): Promise<boolean> {
    try {
      // Intentar hacer una consulta simple para verificar si la tabla existe
      const { error } = await supabase
        .from('reports')
        .select('id')
        .limit(1);
      
      if (error && error.message.includes('relation "reports" does not exist')) {
        console.warn('La tabla reports no existe. Por favor, ejecute el script SQL para crearla.');
        return false;
      }
      return true;
    } catch (err) {
      console.warn('Error verificando tabla reports:', err);
      return false;
    }
  }

  async createReport(reportData: CreateReportData): Promise<Report> {
    const tableExists = await this.ensureTableExists();
    if (!tableExists) {
      throw new Error('La tabla reports no existe. Por favor, ejecute el script SQL para crearla.');
    }
    
    const { data, error } = await supabase
      .from('reports')
      .insert(reportData)
      .select()
      .single();

    if (error) {
      console.error('Error creating report:', error);
      throw new Error(`Error al crear el informe: ${error.message}`);
    }

    return data;
  }

  async getReports(): Promise<Report[]> {
    await this.ensureTableExists();
    
    const { data, error } = await supabase
      .from('reports')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching reports:', error);
      throw new Error(`Error al obtener los informes: ${error.message}`);
    }

    return data || [];
  }

  async getReportById(id: string): Promise<Report | null> {
    const { data, error } = await supabase
      .from('reports')
      .select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching report:', error);
      throw new Error(`Error al obtener el informe: ${error.message}`);
    }

    return data;
  }

  async getReportsByPatient(patientId: string): Promise<Report[]> {
    const { data, error } = await supabase
      .from('reports')
      .select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        )
      `)
      .eq('patient_id', patientId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching patient reports:', error);
      throw new Error(`Error al obtener los informes del paciente: ${error.message}`);
    }

    return data || [];
  }

  async deleteReport(id: string): Promise<void> {
    const { error } = await supabase
      .from('reports')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting report:', error);
      throw new Error(`Error al eliminar el informe: ${error.message}`);
    }
  }

  async updateReport(id: string, updates: Partial<CreateReportData>): Promise<Report> {
    const { data, error } = await supabase
      .from('reports')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        )
      `)
      .single();

    if (error) {
      console.error('Error updating report:', error);
      throw new Error(`Error al actualizar el informe: ${error.message}`);
    }

    return data;
  }

  // Legacy methods for backward compatibility
  async generateReport(patientId: string, questionnaireId: string): Promise<ApiResponse<TestScores>> {
    // This method can be implemented later if needed
    throw new Error('Method not implemented');
  }

  async getPatientHistory(patientId: string): Promise<ApiResponse<TestScores[]>> {
    // This method can be implemented later if needed
    throw new Error('Method not implemented');
  }

  async exportReport(reportId: string, format: 'pdf' | 'csv'): Promise<Blob> {
    // This method can be implemented later if needed
    throw new Error('Method not implemented');
  }
}

export const reportsService = new ReportsService();
export default reportsService;