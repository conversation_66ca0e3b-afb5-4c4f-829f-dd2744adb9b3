import React, { useState, useEffect, useCallback } from 'react';
import { usePatientsStore } from '../stores';
import {
  Box,
  Container,
  Typography,
  Paper,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Add as AddIcon,
  People as PeopleIcon
} from '@mui/icons-material';


// Utility function for age calculation, handles null/undefined/invalid dates
const calculateAge = (birthDate) => {
    if (!birthDate) return ''; // Handle missing birthDate

    const today = new Date();
    const birth = new Date(birthDate);

    // Check if the birthDate is a valid date
    if (isNaN(birth.getTime())) {
        return ''; // Or some other placeholder like "Invalid Date"
    }

    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    return age;
};

const initialFormData = {
  name: '',
  birthDate: '',
  gender: '',
  testDate: '',
  psychologist: ''
};

function Patients() {
  // Zustand store

  const { items: patients, status, error, fetchPatients, deletePatient, setSelectedPatient, clearError, addPatient, updatePatient } = usePatientsStore();
  
  // Local state for UI
  const [open, setOpen] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [formData, setFormData] = useState(initialFormData);
  const [formErrors, setFormErrors] = useState({});
  const [notification, setNotification] = useState({ open: false, message: '', type: 'success' });
  const [loading, setLoading] = useState(false);

  const storeLoading = status === 'loading';

  // Load patients on component mount
  useEffect(() => {
    if (patients.length === 0 && status === 'idle') {
      fetchPatients();
    }
  }, [patients.length, status, fetchPatients]);
  
  // Handle errors from store
  useEffect(() => {
    if (error) {
      setNotification({ open: true, message: error, type: 'error' });
      clearError();
    }
  }, [error, clearError]);


  // Form validation
  const validateForm = useCallback(() => {
    const errors = {};
    if (!formData.name.trim()) errors.name = 'El nombre es requerido';
    if (!formData.birthDate) errors.birthDate = 'La fecha de nacimiento es requerida';
    if (!formData.gender) errors.gender = 'El género es requerido';
    if (!formData.testDate) errors.testDate = 'La fecha de evaluación es requerida';
    if (!formData.psychologist.trim()) errors.psychologist = 'El nombre del psicólogo es requerido';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  // Notify when patients change (This is for other components listening)
  useEffect(() => {
    window.dispatchEvent(new Event('patientsUpdated'));
  }, [patients]);

  // Dialog handlers
  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setEditingId(null);
    setFormData(initialFormData);
    setFormErrors({});
  };

  // Form input handler
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when field is modified
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

// Submit handler
const handleSubmit = useCallback(async () => {
  setLoading(true); // Set loading to true before the request

    try {
        if (!validateForm()) {
          setLoading(false); // Ensure loading is set to false if validation fails
          return; // Stop the function if validation fails
        }

        const formattedData = {
            name: formData.name.trim(),
            birthDate: formData.birthDate, // Send the date as is, Supabase handles it
            gender: formData.gender,
            dateCreated: formData.testDate,  // Send the date as is
            notes: formData.psychologist.trim(),  // "notes" in Supabase
        };

        let response;

      if (editingId !== null) {
        await updatePatient(editingId, formattedData);
        setNotification({ open: true, message: 'Paciente actualizado exitosamente', type: 'success' });
      } else {
        await addPatient(formattedData);
        setNotification({ open: true, message: 'Paciente agregado exitosamente', type: 'success' });
      }
      
      handleClose(); // Close whether it's create or update

    } catch (error) {
      console.error('Error in handleSubmit:', error);
      const errorMessage = error.message || 'Por favor, verifique los datos ingresados';
      setNotification({ open: true, message: errorMessage, type: 'error' });
    } finally {
        setLoading(false);
    }
}, [formData, editingId, validateForm]);


  // Edit handler
const handleEdit = (patient) => {
  setFormData({
    name: patient.name,
    birthDate: patient.birthDate, // Keep dates as strings
    gender: patient.gender,
    testDate: patient.dateCreated,  // Keep dates as strings
    psychologist: patient.notes,
  });
  setEditingId(patient.id);
  setOpen(true);
};

  // Delete handler
  const handleDelete = async (id) => {
    if (window.confirm('¿Está seguro de que desea eliminar este paciente?')) {
      try {
        await deletePatient(id);
        setNotification({ open: true, message: 'Paciente eliminado exitosamente', type: 'success' });
      } catch (error) {
        console.error('Error deleting patient:', error);
        setNotification({ open: true, message: 'Error al eliminar el paciente', type: 'error' });
      }
    }
  };

  // Notification close handler
  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <Box sx={{ 
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    }}>
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 5,
        p: 3,
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #e9ecef'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{
            p: 2,
            borderRadius: '12px',
            backgroundColor: '#e3f2fd',
            mr: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <PeopleIcon sx={{ fontSize: 32, color: '#1976d2' }} />
          </Box>
          <Box>
            <Typography variant="h4" component="h1" sx={{ 
              color: '#2c3e50', 
              fontWeight: 600,
              mb: 0.5,
              fontSize: '2rem'
            }}>
              Gestión de Pacientes
            </Typography>
            <Typography variant="body1" sx={{ color: '#7f8c8d', fontSize: '1rem' }}>
              Administra la información de tus pacientes
            </Typography>
          </Box>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleOpen}
          sx={{
            backgroundColor: '#27ae60',
            '&:hover': {
              backgroundColor: '#229954',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 20px rgba(39, 174, 96, 0.3)'
            },
            borderRadius: '12px',
            px: 4,
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
            textTransform: 'none',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 12px rgba(39, 174, 96, 0.2)'
          }}
          disabled={loading}
        >
          Nuevo Paciente
        </Button>
      </Box>

      <TableContainer component={Paper} elevation={0} sx={{ 
        borderRadius: '16px', 
        overflow: 'hidden',
        backgroundColor: 'white',
        border: '1px solid #e9ecef',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
      }}>
        <Table>
          <TableHead>
            <TableRow sx={{ 
              backgroundColor: '#f8f9fa',
              borderBottom: '2px solid #e9ecef'
            }}>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Nombre</TableCell>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Edad</TableCell>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Género</TableCell>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Fecha de Nacimiento</TableCell>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Fecha de Evaluación</TableCell>
              <TableCell sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Psicólogo</TableCell>
              <TableCell align="center" sx={{ 
                fontWeight: 600, 
                color: '#2c3e50',
                fontSize: '0.95rem',
                py: 2.5
              }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {storeLoading ? ( // Show loading indicator
              <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 6 }}>
                    <Box sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center',
                      gap: 2
                    }}>
                      <Box sx={{
                        width: 40,
                        height: 40,
                        border: '4px solid #e9ecef',
                        borderTop: '4px solid #3498db',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' }
                        }
                      }} />
                      <Typography sx={{ color: '#7f8c8d', fontSize: '1rem' }}>
                        Cargando pacientes...
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
            ) : patients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 8 }}>
                  <Box sx={{ 
                    textAlign: 'center',
                    p: 4,
                    backgroundColor: '#f8f9fa',
                    borderRadius: '12px',
                    border: '2px dashed #dee2e6'
                  }}>
                    <PeopleIcon sx={{ 
                      fontSize: 64, 
                      color: '#bdc3c7', 
                      mb: 2 
                    }} />
                    <Typography variant="h6" sx={{ 
                      color: '#7f8c8d', 
                      fontWeight: 500,
                      mb: 1
                    }}>
                      No hay pacientes registrados
                    </Typography>
                    <Typography sx={{ 
                      color: '#95a5a6',
                      fontSize: '0.95rem'
                    }}>
                      Haga clic en "Nuevo Paciente" para agregar uno.
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              patients.map((patient) => (
                <TableRow
                  key={patient.id}
                  sx={{
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: '#f8f9fa',
                      transform: 'scale(1.01)',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    },
                    borderBottom: '1px solid #f1f2f6'
                  }}
                >
                  <TableCell sx={{ py: 2.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{
                        p: 1,
                        borderRadius: '8px',
                        backgroundColor: patient.gender === 'M' ? '#e3f2fd' : '#fce4ec',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        {patient.gender === 'M' ? (
                          <MaleIcon sx={{ color: '#1976d2', fontSize: '1.2rem' }} />
                        ) : (
                          <FemaleIcon sx={{ color: '#c2185b', fontSize: '1.2rem' }} />
                        )}
                      </Box>
                      <Typography sx={{ 
                        fontWeight: 500, 
                        color: '#2c3e50',
                        fontSize: '0.95rem'
                      }}>
                        {patient.name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ py: 2.5 }}>
                    <Typography sx={{ color: '#2c3e50', fontSize: '0.9rem' }}>
                      {calculateAge(patient.birthDate)} años
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2.5 }}>
                    <Box sx={{
                      px: 2,
                      py: 0.5,
                      borderRadius: '16px',
                      backgroundColor: patient.gender === 'M' ? '#e3f2fd' : '#fce4ec',
                      display: 'inline-block'
                    }}>
                      <Typography sx={{ 
                        color: patient.gender === 'M' ? '#1976d2' : '#c2185b',
                        fontSize: '0.85rem',
                        fontWeight: 500
                      }}>
                        {patient.gender === 'M' ? 'Masculino' : 'Femenino'}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ py: 2.5 }}>
                    <Typography sx={{ color: '#2c3e50', fontSize: '0.9rem' }}>
                      {new Date(patient.birthDate).toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/-/g, '/')}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2.5 }}>
                    <Typography sx={{ color: '#2c3e50', fontSize: '0.9rem' }}>
                      {new Date(patient.dateCreated).toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/-/g, '/')}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2.5 }}>
                    <Typography sx={{ color: '#2c3e50', fontSize: '0.9rem' }}>
                      {patient.notes}
                    </Typography>
                  </TableCell>
                  <TableCell align="center" sx={{ py: 2.5 }}>
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                      <IconButton
                        onClick={() => handleEdit(patient)}
                        sx={{ 
                          backgroundColor: '#e3f2fd',
                          color: '#1976d2',
                          '&:hover': { 
                            backgroundColor: '#bbdefb',
                            transform: 'scale(1.1)'
                          },
                          transition: 'all 0.2s ease',
                          width: 36,
                          height: 36
                        }}
                        disabled={storeLoading}
                      >
                        <EditIcon sx={{ fontSize: '1.1rem' }} />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(patient.id)}
                        sx={{ 
                          backgroundColor: '#ffebee',
                          color: '#d32f2f',
                          '&:hover': { 
                            backgroundColor: '#ffcdd2',
                            transform: 'scale(1.1)'
                          },
                          transition: 'all 0.2s ease',
                          width: 36,
                          height: 36
                        }}
                        disabled={storeLoading}
                      >
                        <DeleteIcon sx={{ fontSize: '1.1rem' }} />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="sm" 
        fullWidth
        disableEnforceFocus
        disableAutoFocus
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: '1px solid #e9ecef', 
          mb: 2,
          pb: 3,
          backgroundColor: '#f8f9fa'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{
              p: 1.5,
              borderRadius: '10px',
              backgroundColor: editingId !== null ? '#fff3e0' : '#e8f5e9',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {editingId !== null ? (
                <EditIcon sx={{ color: '#f57c00', fontSize: '1.5rem' }} />
              ) : (
                <AddIcon sx={{ color: '#2e7d32', fontSize: '1.5rem' }} />
              )}
            </Box>
            <Typography variant="h6" sx={{ 
              color: '#2c3e50',
              fontWeight: 600,
              fontSize: '1.3rem'
            }}>
              {editingId !== null ? 'Editar Paciente' : 'Nuevo Paciente'}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="name"
                label="Nombre Completo"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.name}
                helperText={formErrors.name}
                sx={{ 
                  '& .MuiOutlinedInput-root': { 
                    borderRadius: '10px',
                    '&:hover fieldset': { borderColor: '#3498db' },
                    '&.Mui-focused fieldset': { borderColor: '#3498db' }
                  },
                  '& .MuiInputLabel-root.Mui-focused': { color: '#3498db' }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="birthDate"
                label="Fecha de Nacimiento"
                type="date"
                value={formData.birthDate}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.birthDate}
                helperText={formErrors.birthDate}
                InputLabelProps={{ shrink: true }}
                sx={{ '& .MuiOutlinedInput-root': { '&:hover fieldset': { borderColor: '#2e7d32' } } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!formErrors.gender}>
                <InputLabel>Género</InputLabel>
                <Select
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  label="Género"
                  sx={{ 
                    borderRadius: '10px',
                    '&:hover .MuiOutlinedInput-notchedOutline': { borderColor: '#3498db' },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: '#3498db' }
                  }}
                >
                  <MenuItem value="M">Masculino</MenuItem>
                  <MenuItem value="F">Femenino</MenuItem>
                </Select>
                {formErrors.gender && <Typography variant="caption" color="error">{formErrors.gender}</Typography>}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="testDate"
                label="Fecha de Evaluación"
                type="date"
                value={formData.testDate}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.testDate}
                helperText={formErrors.testDate}
                InputLabelProps={{ shrink: true }}
                sx={{ '& .MuiOutlinedInput-root': { '&:hover fieldset': { borderColor: '#2e7d32' } } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="psychologist"
                label="Psicólogo Evaluador"
                value={formData.psychologist}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.psychologist}
                helperText={formErrors.psychologist}
                sx={{ '& .MuiOutlinedInput-root': { '&:hover fieldset': { borderColor: '#2e7d32' } } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ 
          p: 3, 
          backgroundColor: '#f8f9fa',
          borderTop: '1px solid #e9ecef',
          gap: 2
        }}>
          <Button
            onClick={handleClose}
            sx={{
              color: '#7f8c8d',
              borderRadius: '10px',
              px: 3,
              py: 1,
              textTransform: 'none',
              fontWeight: 500,
              '&:hover': { 
                backgroundColor: '#ecf0f1',
                color: '#2c3e50'
              }
            }}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            sx={{
              backgroundColor: editingId !== null ? '#f39c12' : '#27ae60',
              borderRadius: '10px',
              px: 4,
              py: 1,
              textTransform: 'none',
              fontWeight: 600,
              boxShadow: editingId !== null 
                ? '0 4px 12px rgba(243, 156, 18, 0.3)'
                : '0 4px 12px rgba(39, 174, 96, 0.3)',
              '&:hover': { 
                backgroundColor: editingId !== null ? '#e67e22' : '#229954',
                transform: 'translateY(-1px)',
                boxShadow: editingId !== null 
                  ? '0 6px 16px rgba(243, 156, 18, 0.4)'
                  : '0 6px 16px rgba(39, 174, 96, 0.4)'
              },
              transition: 'all 0.2s ease'
            }}
            disabled={loading}
          >
            {editingId !== null ? 'Actualizar' : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleNotificationClose}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
    </Box>
  );
}

export default Patients;