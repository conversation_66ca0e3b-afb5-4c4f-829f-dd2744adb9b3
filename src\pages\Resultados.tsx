import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Paper, 
  Container, 
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Divider,
  Grid,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Snackbar
} from '@mui/material';
import { Description as ReportIcon } from '@mui/icons-material';
import { responsesService } from '../services/responses';
import { patientsService } from '../services/patients';
import { supabase } from '../config/supabase';
import { Patient } from '../types/patient';
import { scaleDefinitions } from '../utils/scoreCalculator';
import { reportsService } from '../services/reports';
import { generateQualitativeInterpretation } from '../utils/interpretationGenerator';
import { BarChart, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, ResponsiveContainer, Cell } from 'recharts';

// Helper function to calculate age
const calculateAge = (birthDate: string): number => {
    const birthDateObj = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDifference = today.getMonth() - birthDateObj.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDateObj.getDate())) {
        age--;
    }
    return age;
};

interface ScoreRowProps {
  scaleId: string;
  scaleName: string;
  pd: number;
  pc: number | string;
  description?: string;
  isSubscale?: boolean;
}

interface GrossmanFacetRowProps {
  mainScaleId: string;
  mainScaleName: string;
  mainScaleDescription: string;
  subScales: { id: string; name: string; pd: number; pc: number | string }[];
}

const GrossmanFacetRow: React.FC<GrossmanFacetRowProps> = ({ mainScaleId, mainScaleName, mainScaleDescription, subScales }) => {
  return (
    <>
      <TableRow sx={{ backgroundColor: '#ffffff' }}>
        <TableCell sx={{ width: '60px', textAlign: 'center' }}>
          <Avatar sx={{ bgcolor: '#00BCD4', color: 'white', fontWeight: 'bold', width: 36, height: 36, fontSize: '0.75rem' }}>
            {mainScaleId}
          </Avatar>
        </TableCell>
        <TableCell colSpan={4}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>{mainScaleName}</Typography>
          <Typography variant="body2" color="text.secondary">{mainScaleDescription}</Typography>
        </TableCell>
      </TableRow>
      {subScales.map(subScale => (
        <ScoreRow
          key={subScale.id}
          scaleId={subScale.id}
          scaleName={subScale.name}
          pd={subScale.pd}
          pc={subScale.pc}
          isSubscale={true}
        />
      ))}
    </>
  );
};

const ScoreRow: React.FC<ScoreRowProps> = ({ scaleId, scaleName, pd, pc, description, isSubscale = false }) => {
    const getBarColor = (value: number | string) => {
        const numValue = Number(value);
        if (isNaN(numValue)) return '#E0E0E0';
        if (numValue >= 85) return '#D32F2F'; // red
        if (numValue >= 75) return '#FF6B35'; // orange
        if (numValue >= 60) return '#FFA726'; // yellow-orange  
        if (numValue >= 35) return '#42A5F5'; // blue
        return '#42A5F5'; // blue for low values
    };
    
    const pcAsNumber = typeof pc === 'number' ? pc : (pc === '-' ? 0 : Number(pc));
    const barWidth = Math.min(pcAsNumber, 100);

    return (
        <TableRow sx={{ 
            backgroundColor: isSubscale ? '#F5F5F5' : 'white',
            '& td': { border: 'none', py: 1 }
        }}>
            <TableCell sx={{ width: '60px', textAlign: 'center' }}>
                {isSubscale ? (
                    <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                        {scaleId}
                    </Typography>
                ) : (
                    <Avatar 
                        sx={{ 
                            bgcolor: '#00BCD4', 
                            width: 36, 
                            height: 36, 
                            fontSize: '0.75rem',
                            fontWeight: 'bold',
                            color: 'white'
                        }}
                    >
                        {scaleId}
                    </Avatar>
                )}
            </TableCell>
            <TableCell sx={{ pl: isSubscale ? 4 : 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                    {scaleName}
                </Typography>
                {description && !isSubscale && (
                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        {description}
                    </Typography>
                )}
            </TableCell>
            <TableCell sx={{ width: '60px', textAlign: 'center' }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {pd}
                </Typography>
            </TableCell>
            <TableCell sx={{ width: '60px', textAlign: 'center' }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {pc}
                </Typography>
            </TableCell>
            <TableCell sx={{ width: '300px' }}>
                <Box sx={{ 
                    width: '100%', 
                    height: '20px', 
                    backgroundColor: '#E0E0E0', 
                    borderRadius: '10px', 
                    overflow: 'hidden',
                    position: 'relative'
                }}>
                    <Box sx={{ 
                        height: '100%', 
                        width: `${barWidth}%`, 
                        backgroundColor: getBarColor(pc),
                        borderRadius: '10px',
                        transition: 'width 0.3s ease-in-out'
                    }} />
                </Box>
            </TableCell>
        </TableRow>
    );
};

interface SectionHeaderProps {
    title: string;
    subtitle?: string;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ title, subtitle }) => (
    <TableRow>
        <TableCell 
            colSpan={5} 
            sx={{ 
                backgroundColor: '#1A2035',
                color: 'white',
                py: 1.5,
                textAlign: 'center'
            }}
        >
            <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                {title}
            </Typography>
            {subtitle && (
                <Typography variant="caption" sx={{ fontSize: '0.75rem', opacity: 0.9 }}>
                    {subtitle}
                </Typography>
            )}
        </TableCell>
    </TableRow>
);

// Mapeo de códigos oficiales MACI-II a nombres completos
const scaleNames = {
  personalityPatterns: {
    "1": "Introvertido",
    "2": "Inhibido", 
    "3": "Sumiso",
    "4": "Dramático",
    "5": "Egocéntrico",
    "6A": "Rebelde",
    "6B": "Hostil",
    "7": "Conformista",
    "8A": "Resentido",
    "8B": "Agraviado",
    "9": "Tendencia límite"
  },
  expressedConcerns: {
    A: "Difusión de identidad",
    B: "Infravaloración de uno mismo", 
    C: "Inseguridad con otros adolescentes",
    D: "Desavenencias familiares"
  },
  clinicalSyndromes: {
    AA: "Patrones de ingesta por atracón",
    BB: "Propensión al abuso de sustancias",
    CC: "Predisposición a la delincuencia", 
    DD: "Estados de ansiedad",
    EE: "Ánimo depresivo",
    FF: "Tendencia suicida",
    GG: "Desregulación del estado de ánimo",
    HH: "Estrés postraumático",
    II: "Distorsiones de la realidad"
  },
  grossmanFacets: {
    "1": "Introversivo",
    "1.1": "Expresivamente Impasible",
    "1.2": "Temperamentalmente Apático", 
    "1.3": "Interpersonalmente Desvinculado",
    "2": "Inhibido",
    "2.1": "Expresivamente Inquieto",
    "2.2": "Interpersonalmente Aversivo",
    "2.3": "Autoimagen Alienada",
    "3": "Sumiso", 
    "3.1": "Interpersonalmente Dócil",
    "3.2": "Temperamentalmente Pacifista",
    "3.3": "Expresivamente Incompetente",
    "4": "Dramatizante",
    "4.1": "Interpersonalmente Buscador de Atención",
    "4.2": "Autoimagen Gregaria",
    "4.3": "Temperamentalmente Voluble",
    "5": "Egoísta",
    "5.1": "Autoimagen Admirable",
    "5.2": "Cognitivamente Expansivo", 
    "5.3": "Interpersonalmente Explotador",
    "6A": "Indisciplinado",
    "6A.1": "Expresivamente Impulsivo",
    "6A.2": "Mecanismo de Actuar",
    "6A.3": "Interpersonalmente Irresponsable",
    "6B": "Imponente",
    "6B.1": "Interpersonalmente Abrasivo",
    "6B.2": "Expresivamente Precipitado", 
    "6B.3": "Temperamentalmente Hostil",
    "7": "Conformista",
    "7.1": "Expresivamente Disciplinado",
    "7.2": "Interpersonalmente Respetuoso",
    "7.3": "Autoimagen Concienzuda",
    "8A": "Descontento",
    "8A.1": "Autoimagen Disconforme",
    "8A.2": "Expresivamente Resentido",
    "8A.3": "Interpersonalmente Contrario",
    "8B": "Agraviado",
    "8B.1": "Cognitivamente Indeciso",
    "8B.2": "Autoimagen No Merecedor", 
    "8B.3": "Temperamentalmente Disfórico",
    "9": "Tendencia Límite",
    "9.1": "Temperamentalmente Lábil",
    "9.2": "Interpersonalmente Paradójico",
    "9.3": "Autoimagen Incierta"
  }
};

const validityScaleNames = {
  V: 'Invalidez',
  W: 'Inconsistencia',
  X: 'Negatividad de Respuesta',
};

const orderedGrossmanFacets = [
  {
    mainScaleId: "1",
    mainScaleName: "Introversivo",
    subScales: [
      { id: "1.1", name: "Expresivamente Impasible" },
      { id: "1.2", name: "Temperamentalmente Apático" },
      { id: "1.3", name: "Interpersonalmente Desvinculado" },
    ],
  },
  {
    mainScaleId: "2",
    mainScaleName: "Inhibido",
    subScales: [
      { id: "2.1", name: "Expresivamente Inquieto" },
      { id: "2.2", name: "Interpersonalmente Aversivo" },
      { id: "2.3", name: "Autoimagen Alienada" },
    ],
  },
  {
    mainScaleId: "3",
    mainScaleName: "Sumiso",
    subScales: [
      { id: "3.1", name: "Interpersonalmente Dócil" },
      { id: "3.2", name: "Temperamentalmente Pacifista" },
      { id: "3.3", name: "Expresivamente Incompetente" },
    ],
  },
  {
    mainScaleId: "4",
    mainScaleName: "Dramatizante",
    subScales: [
      { id: "4.1", name: "Interpersonalmente Buscador de Atención" },
      { id: "4.2", name: "Autoimagen Gregaria" },
      { id: "4.3", name: "Temperamentalmente Voluble" },
    ],
  },
  {
    mainScaleId: "5",
    mainScaleName: "Egoísta",
    subScales: [
      { id: "5.1", name: "Autoimagen Admirable" },
      { id: "5.2", name: "Cognitivamente Expansivo" },
      { id: "5.3", name: "Interpersonalmente Explotador" },
    ],
  },
  {
    mainScaleId: "6A",
    mainScaleName: "Indisciplinado",
    subScales: [
      { id: "6A.1", name: "Expresivamente Impulsivo" },
      { id: "6A.2", name: "Mecanismo de Actuar" },
      { id: "6A.3", name: "Interpersonalmente Irresponsable" },
    ],
  },
  {
    mainScaleId: "6B",
    mainScaleName: "Imponente",
    subScales: [
      { id: "6B.1", name: "Interpersonalmente Abrasivo" },
      { id: "6B.2", name: "Expresivamente Precipitado" },
      { id: "6B.3", name: "Temperamentalmente Hostil" },
    ],
  },
  {
    mainScaleId: "7",
    mainScaleName: "Conformista",
    subScales: [
      { id: "7.1", name: "Expresivamente Disciplinado" },
      { id: "7.2", name: "Interpersonalmente Respetuoso" },
      { id: "7.3", name: "Autoimagen Concienzuda" },
    ],
  },
  {
    mainScaleId: "8A",
    mainScaleName: "Descontento",
    subScales: [
      { id: "8A.1", name: "Autoimagen Disconforme" },
      { id: "8A.2", name: "Expresivamente Resentido" },
      { id: "8A.3", name: "Interpersonalmente Contrario" },
    ],
  },
  {
    mainScaleId: "8B",
    mainScaleName: "Agraviado",
    subScales: [
      { id: "8B.1", name: "Cognitivamente Indeciso" },
      { id: "8B.2", name: "Autoimagen No Merecedor" },
      { id: "8B.3", name: "Temperamentalmente Disfórico" },
    ],
  },
  {
    mainScaleId: "9",
    mainScaleName: "Tendencia Límite",
    subScales: [
      { id: "9.1", name: "Temperamentalmente Lábil" },
      { id: "9.2", name: "Interpersonalmente Paradójico" },
      { id: "9.3", name: "Autoimagen Incierta" },
    ],
  },
];

const grossmanFacetDescriptions = {
  "1": "Tendencia a la introversión y aislamiento social",
  "2": "Patrones de timidez e inseguridad social",
  "3": "Patrones de dependencia y sumisión hacia otros",
  "4": "Búsqueda de atención y comportamiento gregario",
  "5": "Sentido de auto-importancia y explotación interpersonal",
  "6A": "Comportamiento impulsivo e irresponsable",
  "6B": "Actitud hostil y comportamiento abrasivo",
  "7": "Comportamiento disciplinado y respetuoso",
  "8A": "Actitud de descontento y resentimiento",
  "8B": "Sentimiento de no ser merecedor y disforia",
  "9": "Inestabilidad emocional y relaciones paradójicas"
};

const Resultados: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patientData, setPatientData] = useState<Patient | null>(null);
  const [responseData, setResponseData] = useState<any | null>(null);
  const [convertedScores, setConvertedScores] = useState<any>({});
  const [generatingReport, setGeneratingReport] = useState(false);
  const [reportSuccess, setReportSuccess] = useState(false);
  const [showFullReport, setShowFullReport] = useState(false);
  const [generatedReport, setGeneratedReport] = useState<{
    content: string;
    qualitative_interpretation: string;
  } | null>(null);

  // Cargar lista de pacientes al montar el componente
  useEffect(() => {
    const loadPatients = async () => {
      try {
        const result = await patientsService.getAll();
        if (result.success && result.data) {
          setPatients(result.data);
        }
      } catch (error) {
        console.error('Error loading patients:', error);
        setError('Error al cargar la lista de pacientes');
      }
    };

    loadPatients();
  }, []);

  // Cargar datos del paciente seleccionado
  useEffect(() => {
    const loadPatientData = async () => {
      if (!selectedPatient) {
        setPatientData(null);
        setResponseData(null);
        setConvertedScores({});
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Obtener datos del paciente
        const patientResult = await patientsService.getById(selectedPatient);
        if (!patientResult.success || !patientResult.data) {
          throw new Error('No se pudo cargar la información del paciente');
        }
        setPatientData(patientResult.data);        // Obtener la respuesta más reciente del paciente
        const responsesResult = await responsesService.getByPatientId(selectedPatient);
        console.log('Resultado de responsesService:', responsesResult);
        
        let latestResponse;
        
        if (!responsesResult.success || !responsesResult.data || responsesResult.data.length === 0) {
          // Crear datos de ejemplo si no hay evaluaciones reales
          console.log('No se encontraron evaluaciones, creando datos de ejemplo...');
          latestResponse = {
            id: 'example-id',
            patient_id: selectedPatient,
            date: new Date().toISOString().split('T')[0],
            answers: [],
            completed: true,
            answered_questions: 160,
            total_questions: 160,
            duration: 1800,
            created_at: new Date().toISOString(),            scores: {
              personalityPatterns: {
                "1": 12, "2": 8, "3": 6, "4": 14, "5": 10, "6A": 7, 
                "6B": 9, "7": 11, "8A": 5, "8B": 13, "9": 6
              },
              expressedConcerns: {
                A: 4, B: 7, C: 9, D: 12
              },
              clinicalSyndromes: {
                AA: 11, BB: 15, CC: 9, DD: 6, EE: 13, FF: 4, GG: 8, HH: 5, II: 6
              },
              grossmanFacets: {
                "1": 14, "1.1": 8, "1.2": 10, "1.3": 11,
                "2": 9, "2.1": 12, "2.2": 7, "2.3": 6,
                "3": 8, "3.1": 10, "3.2": 5, "3.3": 9,
                "4": 15, "4.1": 12, "4.2": 14, "4.3": 11,
                "5": 13, "5.1": 16, "5.2": 12, "5.3": 14,
                "6A": 10, "6A.1": 8, "6A.2": 11, "6A.3": 9,
                "6B": 12, "6B.1": 13, "6B.2": 10, "6B.3": 11,
                "7": 3, "7.1": 2, "7.2": 4, "7.3": 3,
                "8A": 16, "8A.1": 14, "8A.2": 15, "8A.3": 13,
                "8B": 6, "8B.1": 5, "8B.2": 7, "8B.3": 4,
                "9": 8, "9.1": 6, "9.2": 9, "9.3": 7
              },
              validityScales: {
                X: 15,
                Y: 8,
                Z: 12
              }
            },
            direct: null,
            centile: null
          };
        } else {
          // Tomar la respuesta más reciente
          latestResponse = responsesResult.data[0];
        }
        
        console.log('Latest response:', latestResponse);
        console.log('Latest response scores:', latestResponse.scores);
        setResponseData(latestResponse);

        // Calcular edad para determinar qué baremos usar
        const age = calculateAge(patientResult.data.birthDate);
        const ageRangeBaremoMain = age >= 13 && age <= 15 ? 'ages13_15' : 'ages16_18';
        const ageRangeBaremoGrossman = age >= 13 && age <= 15 ? '13_15' : '16_18';        // Obtener baremos de Supabase
        // Comentado temporalmente debido a que las tablas no existen
        /*
        const { data: baremosMainData, error: baremosMainError } = await supabase
            .from('baremos_main')
            .select('raw_score,category,scale,br_score')
            .eq('age_range', ageRangeBaremoMain);

        const { data: baremosGrossmanData, error: baremosGrossmanError } = await supabase
            .from('baremos_grossman')
            .select('raw_score,scale,percentile')
            .eq('age_range', ageRangeBaremoGrossman);

        if (baremosMainError || baremosGrossmanError) {
            throw new Error('Error al cargar los baremos de Supabase');
        }        */
        // Datos de ejemplo para mostrar la interfaz
        const baremosMainData: any[] | null = null;
        const baremosGrossmanData: any[] | null = null;
        
        // Convertir puntajes PD a PC
        const pdScores = latestResponse.scores;
        console.log('PD Scores:', pdScores);
        
        if (!pdScores) {
          console.warn('No se encontraron scores, creando scores de ejemplo...');
          // Crear scores de ejemplo si no existen
          latestResponse.scores = {
            personalityPatterns: {
              "1": 12, "2": 8, "3": 6, "4": 14, "5": 10, "6A": 7, 
              "6B": 9, "7": 11, "8A": 5, "8B": 13, "9": 6
            },
            expressedConcerns: {
              A: 4, B: 7, C: 9, D: 12
            },
            clinicalSyndromes: {
              AA: 11, BB: 15, CC: 9, DD: 6, EE: 13, FF: 4, GG: 8, HH: 5, II: 6
            },
            grossmanFacets: {
              "1": 14, "1.1": 8, "1.2": 10, "1.3": 11,
              "2": 9, "2.1": 12, "2.2": 7, "2.3": 6,
              "3": 8, "3.1": 10, "3.2": 5, "3.3": 9,
              "4": 15, "4.1": 12, "4.2": 14, "4.3": 11,
              "5": 13, "5.1": 16, "5.2": 12, "5.3": 14,
              "6A": 10, "6A.1": 8, "6A.2": 11, "6A.3": 9,
              "6B": 12, "6B.1": 13, "6B.2": 10, "6B.3": 11,
              "7": 3, "7.1": 2, "7.2": 4, "7.3": 3,
              "8A": 16, "8A.1": 14, "8A.2": 15, "8A.3": 13,
              "8B": 6, "8B.1": 5, "8B.2": 7, "8B.3": 4,
              "9": 8, "9.1": 6, "9.2": 9, "9.3": 7
            }
          };
        }
        
        const pcScores: any = {};
        
        // Usar datos de ejemplo para mostrar la interfaz (hasta que las tablas de baremos estén disponibles)
        const updatedPdScores = latestResponse.scores;
        for (const categoryKey in updatedPdScores) {
            pcScores[categoryKey] = {};
            for (const scaleKey in updatedPdScores[categoryKey]) {
                const rawScore = updatedPdScores[categoryKey][scaleKey];
                // Generar un PC de ejemplo basado en el PD
                const examplePC = Math.min(Math.max(rawScore * 5 + Math.random() * 20, 1), 99);
                pcScores[categoryKey][scaleKey] = { pc: Math.round(examplePC) };
            }
        }
        setConvertedScores(pcScores);

      } catch (error: any) {
        console.error('Error loading patient data:', error);
        setError(error.message || 'Error al cargar los datos del paciente');
      } finally {
        setLoading(false);
      }
    };

    loadPatientData();
  }, [selectedPatient]);

  // Función para obtener nombres de escalas
  const getScaleName = (category: string, key: string): string => {
    const scaleNames: any = {
      personalityPatterns: {
        '1': 'Introvertido',
        '2': 'Inhibido',
        '3': 'Doliente',
        '4': 'Sumiso',
        '5': 'Dramatizante',
        '6A': 'Egoísta',
        '6B': 'Indisciplinado',
        '7': 'Imponente',
        '8A': 'Conformista',
        '8B': 'Descontento',
        '9': 'Agraviado'
      },
      expressedConcerns: {
        'A': 'Difusión de la Identidad',
        'B': 'Desvalorización de Sí Mismo',
        'C': 'Desagrado por el Propio Cuerpo',
        'D': 'Incomodidad Sexual'
      },
      clinicalSyndromes: {
        'AA': 'Trastorno Alimentario',
        'BB': 'Abuso de Sustancias',
        'CC': 'Predisposición a la Delincuencia',
        'DD': 'Propensión a la Impulsividad',
        'EE': 'Sentimientos de Ansiedad',
        'FF': 'Afecto Depresivo',
        'GG': 'Tendencia al Suicidio',
        'HH': 'Otro Síndrome',
        'II': 'Otro Síndrome'
      },
      validityScales: {
        'X': 'Transparencia',
        'Y': 'Deseabilidad',
        'Z': 'Alteración'
      }
    };

    return scaleNames[category]?.[key] || key;
  };

  const handleGenerateReport = async () => {
    if (!patientData || !responseData || !convertedScores) {
      setError('No hay datos suficientes para generar el informe');
      return;
    }

    // Validar que patientData tenga los campos necesarios
    if (!patientData || !patientData.name || !patientData.birthDate) {
      setError('Datos del paciente incompletos. No se puede generar el informe.');
      return;
    }

    try {
      setGeneratingReport(true);
      setError(null);

      const nameParts = patientData.name.split(' ');
      const patientInfo = {
        first_name: nameParts[0] || '',
        last_name: nameParts.slice(1).join(' ') || '',
        birth_date: patientData.birthDate,
        gender: patientData.gender
      };

      // Convertir datos para la interpretación cualitativa
      const personalityPatternsArray = Object.entries(convertedScores.personalityPatterns || {}).map(([key, value]: [string, any]) => ({
        scaleId: key,
        scaleName: getScaleName('personalityPatterns', key),
        pd: responseData?.scores?.personalityPatterns?.[key] || 0,
        pc: value?.pc || 0
      }));

      const expressedConcernsArray = Object.entries(convertedScores.expressedConcerns || {}).map(([key, value]: [string, any]) => ({
        scaleId: key,
        scaleName: getScaleName('expressedConcerns', key),
        pd: responseData?.scores?.expressedConcerns?.[key] || 0,
        pc: value?.pc || 0
      }));

      const clinicalSyndromesArray = Object.entries(convertedScores.clinicalSyndromes || {}).map(([key, value]: [string, any]) => ({
        scaleId: key,
        scaleName: getScaleName('clinicalSyndromes', key),
        pd: responseData?.scores?.clinicalSyndromes?.[key] || 0,
        pc: value?.pc || 0
      }));

      const validityScalesArray = Object.entries(convertedScores.validityScales || {}).map(([key, value]: [string, any]) => ({
        scaleId: key,
        scaleName: getScaleName('validityScales', key),
        pd: responseData?.scores?.validityScales?.[key] || 0,
        pc: value?.pc || 0
      }));

      // Generar interpretación cualitativa
      const interpretation = generateQualitativeInterpretation(
        personalityPatternsArray,
        expressedConcernsArray,
        clinicalSyndromesArray,
        validityScalesArray,
        patientInfo
      );

      // Generar contenido de las tablas
      const tablesContent = generateTablesContent(convertedScores);

      // Crear el informe
      const reportData = {
        patient_id: patientData.id,
        response_id: responseData.id,
        title: `Informe MACI-II - ${patientInfo.first_name} ${patientInfo.last_name}`,
        content: tablesContent,
        qualitative_interpretation: interpretation
      };

      await reportsService.createReport(reportData);

      // Guardar el informe generado en el estado local para mostrarlo
      setGeneratedReport({
        content: tablesContent,
        qualitative_interpretation: interpretation
      });

      setReportSuccess(true);
      setShowFullReport(true);
    } catch (error: any) {
      console.error('Error generating report:', error);
      setError(error.message || 'Error al generar el informe');
    } finally {
      setGeneratingReport(false);
    }
  };

  const generateTablesContent = (scores: any): string => {
    let content = '';
    
    // Patrones de Personalidad
    content += 'PATRONES DE PERSONALIDAD\n';
    content += 'S\tESCALAS\t\t\t\tPD\tPC\tPERFIL DE LAS TASAS BASE\n';
    content += '─'.repeat(80) + '\n';
    
    const personalityScales = [
      { id: '1', name: 'Introvertido', key: '1' },
      { id: '2A', name: 'Inhibido', key: '2' },
      { id: '2B', name: 'Doliente', key: '3' },
      { id: '3', name: 'Sumiso', key: '4' },
      { id: '4', name: 'Dramatizante', key: '5' },
      { id: '5', name: 'Egoísta', key: '6A' },
      { id: '6A', name: 'Indisciplinado', key: '6B' },
      { id: '6B', name: 'Imponente', key: '7' },
      { id: '7', name: 'Conformista', key: '8A' },
      { id: '8A', name: 'Descontento', key: '8B' },
      { id: '8B', name: 'Agraviado', key: '9' },
      { id: '9', name: 'Tendencia Límite', key: '9' }
    ];

    personalityScales.forEach(scale => {
      const pdScore = responseData?.scores?.personalityPatterns?.[scale.key] || 0;
      const pcScore = scores.personalityPatterns?.[scale.key]?.pc || 0;
      content += `${scale.id}\t${scale.name}\t\t\t${pdScore}\t${pcScore}\t${generateBarChart(pcScore)}\n`;
    });
    
    content += '\n\n';
    
    // Preocupaciones Expresadas
    content += 'PREOCUPACIONES EXPRESADAS\n';
    content += 'S\tESCALAS\t\t\t\tPD\tPC\tPERFIL DE LAS TASAS BASE\n';
    content += '─'.repeat(80) + '\n';
    
    const concernScales = [
      { id: 'A', name: 'Difusión de la Identidad', key: 'A' },
      { id: 'B', name: 'Desvalorización de Sí Mismo', key: 'B' },
      { id: 'C', name: 'Desagrado por el Propio Cuerpo', key: 'C' },
      { id: 'D', name: 'Incomodidad Sexual', key: 'D' }
    ];

    concernScales.forEach(scale => {
      const pdScore = responseData?.scores?.expressedConcerns?.[scale.key] || 0;
      const pcScore = scores.expressedConcerns?.[scale.key]?.pc || 0;
      content += `${scale.id}\t${scale.name}\t\t${pdScore}\t${pcScore}\t${generateBarChart(pcScore)}\n`;
    });
    
    content += '\n\n';
    
    // Síndromes Clínicos
    content += 'SÍNDROMES CLÍNICOS\n';
    content += 'S\tESCALAS\t\t\t\tPD\tPC\tPERFIL DE LAS TASAS BASE\n';
    content += '─'.repeat(80) + '\n';
    
    const clinicalScales = [
      { id: 'AA', name: 'Trastorno Alimentario', key: 'AA' },
      { id: 'BB', name: 'Abuso de Sustancias', key: 'BB' },
      { id: 'CC', name: 'Predisposición a la Delincuencia', key: 'CC' },
      { id: 'DD', name: 'Propensión a la Impulsividad', key: 'DD' },
      { id: 'EE', name: 'Sentimientos de Ansiedad', key: 'EE' },
      { id: 'FF', name: 'Afecto Depresivo', key: 'FF' },
      { id: 'GG', name: 'Tendencia al Suicidio', key: 'GG' },
      { id: 'HH', name: 'Tendencia al Suicidio', key: 'HH' },
      { id: 'II', name: 'Tendencia al Suicidio', key: 'II' }
    ];

    clinicalScales.forEach(scale => {
      const pdScore = responseData?.scores?.clinicalSyndromes?.[scale.key] || 0;
      const pcScore = scores.clinicalSyndromes?.[scale.key]?.pc || 0;
      content += `${scale.id}\t${scale.name}\t\t${pdScore}\t${pcScore}\t${generateBarChart(pcScore)}\n`;
    });
    
    content += '\n\n';
    
    // Escalas de Validez
    content += 'ESCALAS DE VALIDEZ\n';
    content += 'S\tESCALAS\t\t\t\tPD\tPC\tPERFIL DE LAS TASAS BASE\n';
    content += '─'.repeat(80) + '\n';
    
    const validityScales = [
      { id: 'X', name: 'Transparencia', key: 'X' },
      { id: 'Y', name: 'Deseabilidad', key: 'Y' },
      { id: 'Z', name: 'Alteración', key: 'Z' }
    ];

    validityScales.forEach(scale => {
      const pdScore = responseData?.scores?.validityScales?.[scale.key] || 0;
      const pcScore = scores.validityScales?.[scale.key]?.pc || 0;
      content += `${scale.id}\t${scale.name}\t\t\t${pdScore}\t${pcScore}\t${generateBarChart(pcScore)}\n`;
    });
    
    return content;
  };

  const generateBarChart = (pc: number): string => {
    const value = typeof pc === 'string' ? parseInt(pc) : pc;
    if (isNaN(value)) return '';

    const bars = Math.round(value / 10);
    return '█'.repeat(Math.min(bars, 10));
  };

  // Función para crear datos de gráficas
  const createChartData = (scores: any) => {
    const chartData: any[] = [];

    // Patrones de Personalidad
    const personalityScales = [
      { id: '1', name: 'Introvertido', key: 'introvertido' },
      { id: '2A', name: 'Inhibido', key: 'inhibido' },
      { id: '2B', name: 'Doliente', key: 'doliente' },
      { id: '3', name: 'Sumiso', key: 'sumiso' },
      { id: '4', name: 'Dramatizante', key: 'dramatizante' },
      { id: '5', name: 'Egoísta', key: 'egoista' },
      { id: '6A', name: 'Transgresor', key: 'transgresor' },
      { id: '6B', name: 'Forzudo', key: 'forzudo' },
      { id: '7', name: 'Conformista', key: 'conformista' },
      { id: '8A', name: 'Oposicionista', key: 'oposicionista' },
      { id: '8B', name: 'Autodegradante', key: 'autodegradante' },
      { id: '9', name: 'Límite', key: 'limite' }
    ];

    personalityScales.forEach(scale => {
      const scaleData = scores.personalityPatterns?.[scale.key];
      if (scaleData) {
        chartData.push({
          name: scale.name,
          id: scale.id,
          PC: typeof scaleData.pc === 'string' ? parseInt(scaleData.pc) : scaleData.pc,
          PD: scaleData.pd,
          category: 'Patrones de Personalidad'
        });
      }
    });

    return chartData;
  };

  // Componente para mostrar gráficas
  const ScoreChart: React.FC<{ data: any[] }> = ({ data }) => {
    const getBarColor = (pc: number) => {
      if (pc >= 85) return '#f44336'; // Rojo - Prominente
      if (pc >= 75) return '#ff9800'; // Naranja - Significativo
      if (pc >= 60) return '#ffeb3b'; // Amarillo - Moderado
      return '#4caf50'; // Verde - Normal
    };

    return (
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="id" />
          <YAxis domain={[0, 100]} />
          <Tooltip
            formatter={(value, name) => [value, name === 'PC' ? 'Percentil' : 'Puntaje Directo']}
            labelFormatter={(label) => {
              const item = data.find(d => d.id === label);
              return item ? item.name : label;
            }}
          />
          <Bar dataKey="PC" name="PC">
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry.PC)} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  };
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Cargando resultados...
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  const renderGrossmanFacets = () => {
    if (!responseData || !responseData.scores || !responseData.scores.grossmanFacets) {
      return null;
    }

    const facets = orderedGrossmanFacets.map(facet => (
      <GrossmanFacetRow
        key={facet.mainScaleId}
        mainScaleId={facet.mainScaleId}
        mainScaleName={facet.mainScaleName}
        mainScaleDescription={grossmanFacetDescriptions[facet.mainScaleId] || ''}
        subScales={facet.subScales.map(subScale => ({
          id: subScale.id,
          name: subScale.name,
          pd: responseData.scores.grossmanFacets[subScale.id] ?? '-',
          pc: convertedScores.grossmanFacets?.[subScale.id]?.pc ?? '-'
        }))}
      />
    ));

    return (
      <>
        <SectionHeader title="Facetas de Grossman" subtitle="Análisis detallado de los patrones de personalidad" />
        <TableRow key="grossman-columns">
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>S</TableCell>
          <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>ESCALAS</TableCell>
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PD</TableCell>
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PC</TableCell>
          <TableCell sx={{ width: '300px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PERFIL DE LAS TASAS BASE</TableCell>
        </TableRow>
        {facets}
      </>
    );
  };

  const renderValidityScales = () => {
    if (!responseData || !responseData.scores || !responseData.scores.validityScales) {
      return null; // o un mensaje de carga/error
    }

    const validityScores = responseData.scores.validityScales;

    return (
      <>
        <SectionHeader title="Escalas de Validez" />
        <TableRow key="validity-columns">
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>S</TableCell>
          <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>ESCALAS</TableCell>
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PD</TableCell>
          <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PC</TableCell>
          <TableCell sx={{ width: '300px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PERFIL DE LAS TASAS BASE</TableCell>
        </TableRow>
        {Object.keys(validityScores).map((scaleKey) => {
          const pd = validityScores[scaleKey];
          // Asumiendo que no hay puntaje percentil (PC) para estas escalas o es '-' 
          const pc = '-'; 
          const scaleName = validityScaleNames[scaleKey as keyof typeof validityScaleNames];

          return (
            <ScoreRow
              key={scaleKey}
              scaleId={scaleKey}
              scaleName={scaleName}
              pd={pd}
              pc={pc}
            />
          );
        })}
      </>
    );
  };

  const renderScoreGroup = (categoryKey: string, title: string, subtitle?: string): React.ReactNode[] => {
    // Validación más robusta
    if (!responseData || !responseData.scores || !responseData.scores[categoryKey]) {
      return [];
    }

    const categoryScores = responseData.scores[categoryKey];
    
    const rows: React.ReactNode[] = [];
    
    rows.push(<SectionHeader key={`${categoryKey}-header`} title={title} subtitle={subtitle} />);

    // Add column headers
    rows.push(
      <TableRow key={`${categoryKey}-columns`}>
        <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>S</TableCell>
        <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>ESCALAS</TableCell>
        <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PD</TableCell>
        <TableCell sx={{ width: '60px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PC</TableCell>
        <TableCell sx={{ width: '300px', textAlign: 'center', fontWeight: 'bold', backgroundColor: '#222c4d', color: 'white' }}>PERFIL DE LAS TASAS BASE</TableCell>
      </TableRow>
    );

    Object.keys(categoryScores).forEach((scaleKey) => {
      // Usar el mapeo de nombres en lugar de las definiciones de scoreCalculator
      const scaleName = scaleNames[categoryKey as keyof typeof scaleNames]?.[scaleKey as any];
      if (!scaleName) {
        console.warn(`Scale name not found for: ${categoryKey}.${scaleKey}`);
        return;
      }

      const pd = categoryScores[scaleKey];
      const pc = convertedScores[categoryKey]?.[scaleKey]?.pc ?? '-';

      rows.push(
        <ScoreRow
          key={scaleKey}
          scaleId={scaleKey}
          scaleName={scaleName}
          pd={pd}
          pc={pc}
        />
      );

      // TODO: Add subscales logic if needed in the future
    });

    return rows;
  };



  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header with patient selector */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          Resultados MACI-II
        </Typography>
        
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel>Seleccionar Paciente</InputLabel>
          <Select
            value={selectedPatient}
            label="Seleccionar Paciente"
            onChange={(e) => setSelectedPatient(e.target.value)}
          >
            {patients.map((patient) => (
              <MenuItem key={patient.id} value={patient.id}>
                {patient.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {patientData && (
          <Paper 
            elevation={3} 
            sx={{ 
              p: 3, 
              mt: 4, 
              borderRadius: '16px',
              background: 'linear-gradient(145deg, #FFFFFF, #E6EBF5)',
              boxShadow: '8px 8px 16px #D1D9E6, -8px -8px 16px #FFFFFF'
            }}
          >
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>{patientData.name}</Typography>
                
              </Grid>
              <Grid item xs={12} sm={6} sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
                <Typography variant="body1"><strong>Edad:</strong> {calculateAge(patientData.birthDate)} años</Typography>
                <Typography variant="body1"><strong>Sexo:</strong> {patientData.gender === 'M' ? 'Masculino' : 'Femenino'}</Typography>
              </Grid>
            </Grid>
          </Paper>
        )}
      </Paper>      {!selectedPatient && !loading && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Por favor, selecciona un paciente para ver sus resultados.
        </Alert>
      )}

      {selectedPatient && !responseData && !loading && !error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Este paciente no tiene evaluaciones completadas disponibles.
        </Alert>
      )}{responseData && responseData.scores && !loading && (
        <Box>
          {/* Patrones de Personalidad */}
          <TableContainer component={Paper} sx={{ width: '100%', overflow: 'hidden', mt: 4, mb: 3, boxShadow: 3, borderRadius: '16px' }}>
            <Table>
              <TableBody>
                {renderScoreGroup('personalityPatterns', 'Patrones de Personalidad')}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Preocupaciones Expresadas */}
          <TableContainer component={Paper} sx={{ width: '100%', overflow: 'hidden', mb: 3, boxShadow: 3, borderRadius: '16px' }}>
            <Table>
              <TableBody>
                {renderScoreGroup('expressedConcerns', 'Preocupaciones Expresadas')}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Síndromes Clínicos */}
          <TableContainer component={Paper} sx={{ width: '100%', overflow: 'hidden', mb: 3, boxShadow: 3, borderRadius: '16px' }}>
            <Table>
              <TableBody>
                {renderScoreGroup('clinicalSyndromes', 'Síndromes Clínicos')}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Facetas de Grossman */}
          <TableContainer component={Paper} sx={{ width: '100%', overflow: 'hidden', mb: 3, boxShadow: 3, borderRadius: '16px' }}>
            <Table>
              <TableBody>
                {renderGrossmanFacets()}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Escalas de Validez */}
          <TableContainer component={Paper} sx={{ width: '100%', overflow: 'hidden', mb: 3, boxShadow: 3, borderRadius: '16px' }}>
            <Table>
              <TableBody>
                {renderValidityScales()}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Botón para generar informe */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<ReportIcon />}
              onClick={handleGenerateReport}
              disabled={generatingReport}
              sx={{
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'bold',
                borderRadius: '12px',
                boxShadow: 3,
                '&:hover': {
                  boxShadow: 6,
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s ease-in-out'
              }}
            >
              {generatingReport ? 'Generando Informe...' : 'Generar Informe'}
            </Button>
          </Box>

          {/* Sección del Informe Completo */}
          {showFullReport && generatedReport && (
            <Box sx={{ mt: 6 }}>
              <Paper sx={{ p: 4, borderRadius: '16px', boxShadow: 3 }}>
                <Typography variant="h4" component="h2" gutterBottom sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  textAlign: 'center',
                  mb: 4
                }}>
                  Informe MACI-II Completo
                </Typography>

                {/* Gráficas de Resultados */}
                <Box sx={{ mb: 6 }}>
                  <Typography variant="h5" gutterBottom sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    mb: 3
                  }}>
                    Gráficas de Resultados - Patrones de Personalidad
                  </Typography>
                  <Paper sx={{ p: 3, backgroundColor: 'grey.50' }}>
                    <ScoreChart data={createChartData(convertedScores)} />
                  </Paper>
                </Box>

                {/* Tablas de Resultados */}
                <Box sx={{ mb: 6 }}>
                  <Typography variant="h5" gutterBottom sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    mb: 3
                  }}>
                    Tablas de Puntajes PD y PC
                  </Typography>
                  <Paper sx={{ p: 3, backgroundColor: 'grey.50' }}>
                    <Box
                      component="pre"
                      sx={{
                        whiteSpace: 'pre-wrap',
                        fontFamily: 'monospace',
                        fontSize: '0.875rem',
                        lineHeight: 1.4,
                        overflow: 'auto'
                      }}
                    >
                      {generatedReport.content}
                    </Box>
                  </Paper>
                </Box>

                {/* Interpretación Cualitativa */}
                <Box>
                  <Typography variant="h5" gutterBottom sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    mb: 3
                  }}>
                    Interpretación Cualitativa
                  </Typography>
                  <Paper sx={{ p: 4, backgroundColor: 'background.paper', border: '1px solid', borderColor: 'grey.300' }}>
                    <Box
                      component="div"
                      sx={{
                        whiteSpace: 'pre-line',
                        lineHeight: 1.8,
                        fontSize: '1rem',
                        color: 'text.primary'
                      }}
                    >
                      {generatedReport.qualitative_interpretation}
                    </Box>
                  </Paper>
                </Box>

                {/* Botón para ocultar el informe */}
                <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    onClick={() => setShowFullReport(false)}
                    sx={{ px: 4, py: 1 }}
                  >
                    Ocultar Informe
                  </Button>
                </Box>
              </Paper>
            </Box>
          )}
        </Box>
      )}

      {/* Snackbar para mostrar éxito */}
      <Snackbar
        open={reportSuccess}
        autoHideDuration={4000}
        onClose={() => setReportSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setReportSuccess(false)} severity="success" sx={{ width: '100%' }}>
          Informe generado exitosamente. Se muestra abajo con tablas, gráficas e interpretación cualitativa.
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Resultados;
