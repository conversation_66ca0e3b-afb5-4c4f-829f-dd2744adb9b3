import React, { Suspense, lazy } from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';

import Patients from './pages/Patients';
import CentileScore from './pages/CentileScore';
import Home from './pages/Home';
import Questionnaire from './pages/Questionnaire';
import Settings from './pages/Settings';
import Admin from './pages/Admin';
import Help from './pages/Help';

// Use lazy loading for components that are not immediately needed
const QuestionnaireResponses = lazy(() => import('./pages/QuestionnaireResponses'));
const DirectScore = lazy(() => import('./pages/DirectScore'));
const Scales = lazy(() => import('./pages/Scales'));
const Respuestas = lazy(() => import('./pages/Respuestas')); // Importar la página de respuestas
const Resultados = lazy(() => import('./pages/Resultados')); // Importar la página de resultados
const Results = lazy(() => import('./pages/Results')); // Importar la página de resultado individual
const Informes = lazy(() => import('./pages/Informes')); // Importar la página de informes
const NotFound = lazy(() => import('./pages/NotFound'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner fullScreen />}>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="pacientes" element={<Patients />} />
          <Route path="cuestionario" element={<Questionnaire />} />
          <Route path="respuestas" element={<QuestionnaireResponses />} />
          <Route path="puntaje-directo" element={<DirectScore />} />
          <Route path="puntaje-centil" element={<CentileScore />} />
          <Route path="escalas" element={<Scales />} />
          <Route path="configuracion" element={<Settings />} />
          <Route path="respuestas" element={<Respuestas />} /> {/* Añadir la ruta de respuestas */}
          <Route path="resultados" element={<Resultados />} /> {/* Añadir la ruta de resultados */}
          <Route path="results/:responseId" element={<Results />} /> {/* Añadir la ruta de resultado individual */}
          <Route path="admin" element={<Admin />} /> {/* Panel de administración */}
          <Route path="ayuda" element={<Help />} /> {/* Centro de ayuda */}
          <Route path="*" element={<NotFound />} /> {/* Ruta catch-all para páginas no encontradas */}
        </Route>
      </Routes>
    </Suspense>
  );
}

export default App;



