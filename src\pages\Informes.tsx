import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Description as ReportIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { reportsService, Report } from '../services/reports';
import DatabaseSetup from '../components/DatabaseSetup';

const Informes: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [showDatabaseSetup, setShowDatabaseSetup] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedReportForMenu, setSelectedReportForMenu] = useState<Report | null>(null);

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      setError('');
      setShowDatabaseSetup(false);
      const data = await reportsService.getReports();
      setReports(data);
    } catch (err) {
      console.error('Error loading reports:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      
      if (errorMessage.includes('tabla reports no existe') || errorMessage.includes('relation "reports" does not exist')) {
        setShowDatabaseSetup(true);
        setError('');
      } else {
        setError('Error al cargar los informes: ' + errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (report: Report) => {
    setSelectedReport(report);
    setDialogOpen(true);
    handleCloseMenu();
  };

  const handleDeleteReport = async (report: Report) => {
    if (window.confirm('¿Está seguro de que desea eliminar este informe?')) {
      try {
        await reportsService.deleteReport(report.id);
        await loadReports(); // Recargar la lista
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error al eliminar el informe');
      }
    }
    handleCloseMenu();
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedReport(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, report: Report) => {
    setAnchorEl(event.currentTarget);
    setSelectedReportForMenu(report);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedReportForMenu(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateAge = (birthDate: string): number => {
    const birthDateObj = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDifference = today.getMonth() - birthDateObj.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDateObj.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          Informes MACI-II
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Gestión y visualización de informes psicológicos generados
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {showDatabaseSetup && (
        <DatabaseSetup onClose={() => setShowDatabaseSetup(false)} />
      )}

      {reports.length === 0 && !showDatabaseSetup && !loading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px',
            textAlign: 'center'
          }}
        >
          <ReportIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No hay informes disponibles
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Los informes generados aparecerán aquí
          </Typography>
        </Box>
      ) : reports.length > 0 ? (
        <Grid container spacing={3}>
          {reports.map((report) => (
            <Grid item xs={12} sm={6} md={4} key={report.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  },
                  cursor: 'pointer'
                }}
                onClick={() => handleViewReport(report)}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <PersonIcon />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h2" noWrap>
                        {report.patient?.first_name} {report.patient?.last_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {report.patient?.gender} • {calculateAge(report.patient?.birth_date || '')} años
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuClick(e, report);
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                    {report.title}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <CalendarIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {formatDate(report.created_at)}
                    </Typography>
                  </Box>

                  <Chip
                    label="Informe Completo"
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button
                    size="small"
                    startIcon={<ViewIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewReport(report);
                    }}
                  >
                    Ver Informe
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Menu contextual */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => selectedReportForMenu && handleViewReport(selectedReportForMenu)}>
          <ViewIcon sx={{ mr: 1 }} />
          Ver Informe
        </MenuItem>
        <MenuItem 
          onClick={() => selectedReportForMenu && handleDeleteReport(selectedReportForMenu)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Eliminar
        </MenuItem>
      </Menu>

      {/* Dialog para mostrar el informe completo */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: '80vh' }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {selectedReport?.title}
            </Typography>
            <Chip
              label={formatDate(selectedReport?.created_at || '')}
              size="small"
              variant="outlined"
            />
          </Box>
          {selectedReport?.patient && (
            <Typography variant="subtitle2" color="text.secondary">
              Paciente: {selectedReport.patient.first_name} {selectedReport.patient.last_name}
            </Typography>
          )}
        </DialogTitle>
        
        <DialogContent dividers>
          {selectedReport && (
            <Box>
              {/* Contenido de las tablas */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main' }}>
                  Resultados de las Escalas
                </Typography>
                <Box
                  component="div"
                  sx={{
                    whiteSpace: 'pre-line',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem',
                    backgroundColor: 'grey.50',
                    p: 2,
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.300'
                  }}
                >
                  {selectedReport.content}
                </Box>
              </Box>

              {/* Interpretación cualitativa */}
              <Box>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main' }}>
                  Interpretación Cualitativa
                </Typography>
                <Box
                  component="div"
                  sx={{
                    whiteSpace: 'pre-line',
                    lineHeight: 1.6,
                    backgroundColor: 'background.paper',
                    p: 3,
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.300'
                  }}
                >
                  {selectedReport.qualitative_interpretation}
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Informes;