import React, { useMemo, useState } from 'react';
import React, { useState, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  Tooltip,
  useTheme,
  useMediaQuery,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Home, 
  People, 
  Quiz, 
  Assessment, 
  AdminPanelSettings, 
  Settings, 
  Help, 
  Logout,
  Star, 
  StarBorder, 
  ExpandLess, 
  ExpandMore,
  BarChart,
  Description
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth'; // Asegúrate de que la ruta sea correcta


function Sidebar() {
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const { logout } = useAuth(); // Hook de autenticación

  const [favorites, setFavorites] = useState(() => {
    const savedFavorites = localStorage.getItem('sidebarFavorites');
    return savedFavorites ? JSON.parse(savedFavorites) : [];
  });

  const [openCategories, setOpenCategories] = useState({});

  const handleCategoryClick = (category) => {
    setOpenCategories(prev => ({ ...prev, [category]: !prev[category] }));
  };

  const toggleFavorite = (path) => {
    const newFavorites = favorites.includes(path)
      ? favorites.filter(p => p !== path)
      : [...favorites, path];
    setFavorites(newFavorites);
    localStorage.setItem('sidebarFavorites', JSON.stringify(newFavorites));
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login'); // Redirige a la página de login
  };

  const menuItems = useMemo(() => [
    { text: 'Inicio', path: '/', icon: <Home />, description: 'Página principal' },
    { text: 'Pacientes', path: '/pacientes', icon: <People />, description: 'Gestionar pacientes' },
    { text: 'Cuestionario', path: '/cuestionario', icon: <Quiz />, description: 'Realizar test MACI-II' },
    { text: 'Respuestas', path: '/respuestas', icon: <Assessment />, description: 'Ver respuestas y resultados de evaluaciones' },
    { text: 'Resultados', path: '/resultados', icon: <BarChart />, description: 'Ver resultados detallados con gráficas' },
    { text: 'Informes', path: '/informes', icon: <Description />, description: 'Gestionar informes psicológicos generados' },
    { text: 'Administración', path: '/admin', icon: <AdminPanelSettings />, description: 'Panel de administración' },
    { text: 'Configuración', path: '/configuracion', icon: <Settings />, description: 'Ajustes del sistema' },
    { text: 'Ayuda', path: '/ayuda', icon: <Help />, description: 'Centro de ayuda' },
  ], []);

  const renderMenuItem = (item) => {
    const isSelected = location.pathname === item.path;
    const isFavorite = favorites.includes(item.path);

    return (
      <ListItem
        button
        component={Link}
        to={item.path}
        key={item.text}
        sx={{
          borderRadius: '4px',
          margin: '4px 8px',
          padding: '10px 16px',
          color: isSelected ? '#FFFFFF' : '#E0E0E0',
          backgroundColor: isSelected ? '#1976D2' : 'transparent',
          transition: 'background-color 0.3s, color 0.3s',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
          },
        }}
      >
        <ListItemIcon sx={{ color: 'inherit', minWidth: '40px' }}>
          {item.icon}
        </ListItemIcon>
        <ListItemText primary={item.text} />
        <IconButton onClick={(e) => { e.preventDefault(); toggleFavorite(item.path); }} size="small">
          {isFavorite ? <Star sx={{ color: '#FFD700' }} /> : <StarBorder sx={{ color: '#E0E0E0' }} />}
        </IconButton>
      </ListItem>
    );
  };

  return (
    <Drawer
      variant={isMobile ? "temporary" : "permanent"}
      sx={{
        width: 250,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 250,
          boxSizing: 'border-box',
          backgroundColor: '#1A2035',
          color: '#E0E0E0',
          borderRight: 'none',
        },
      }}
    >
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="h5" fontWeight="bold" color="#FFFFFF">
          MACI-II
        </Typography>
      </Box>
      <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }} />
      
      <List sx={{ flexGrow: 1 }}>
        {favorites.length > 0 && (
          <>
            <Typography variant="overline" sx={{ padding: '16px', color: '#9E9E9E' }}>Favoritos</Typography>
            {menuItems.filter(item => favorites.includes(item.path)).map(renderMenuItem)}
            <Divider sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', margin: '8px 0' }} />
          </>
        )}
        {menuItems.map(renderMenuItem)}
      </List>

      <Box sx={{ padding: '16px' }}>
        <ListItem
          button
          onClick={handleLogout}
          sx={{
            backgroundColor: '#D32F2F',
            borderRadius: '4px',
            color: '#FFFFFF',
            '&:hover': {
              backgroundColor: '#B71C1C',
            },
          }}
        >
          <ListItemIcon sx={{ color: 'inherit', minWidth: '40px' }}>
            <Logout />
          </ListItemIcon>
          <ListItemText primary="Cerrar Sesión" />
        </ListItem>
      </Box>
    </Drawer>
  );
}

export default Sidebar
